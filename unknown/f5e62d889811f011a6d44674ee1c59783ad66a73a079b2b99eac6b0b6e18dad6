#if USING_APPLOVIN
using System;
using System.Collections.Generic;
using AppsFlyerSDK;
using OnePuz.Services;
using UnityEngine;
#if USING_AMAZON
using AmazonAds;
#endif

namespace OnePuz.Ads
{
    public class Applovin : IAdController
    {
        private bool _isBannerLoaded = false;
        private bool _hasRewarded = false;

        private Action<bool> _onBannerLoaded;
        private Action<bool> _onInterstitialLoaded;
        private Action<bool> _onVideoRewardedLoaded;

        private IAdService _manager;
        private BaseAdUnitDefinition _definitions;

        public bool IsInitialized { get; set; } = false;

        private bool _isFirstLoadInterstitial = true;

        public void Dispose()
        {
            MaxSdkCallbacks.Interstitial.OnAdLoadedEvent -= OnInterstitialLoadedEvent;
            MaxSdkCallbacks.Interstitial.OnAdLoadFailedEvent -= OnInterstitialLoadFailedEvent;
            MaxSdkCallbacks.Interstitial.OnAdDisplayedEvent -= OnInterstitialDisplayedEvent;
            MaxSdkCallbacks.Interstitial.OnAdClickedEvent -= OnInterstitialClickedEvent;
            MaxSdkCallbacks.Interstitial.OnAdHiddenEvent -= OnInterstitialHiddenEvent;
            MaxSdkCallbacks.Interstitial.OnAdDisplayFailedEvent -= OnInterstitialAdFailedToDisplayEvent;

            MaxSdkCallbacks.Banner.OnAdLoadedEvent -= OnBannerAdLoadedEvent;
            MaxSdkCallbacks.Banner.OnAdLoadFailedEvent -= OnBannerAdLoadFailedEvent;
            MaxSdkCallbacks.Banner.OnAdClickedEvent -= OnBannerAdClickedEvent;
            MaxSdkCallbacks.Banner.OnAdRevenuePaidEvent -= OnBannerAdRevenuePaidEvent;
            MaxSdkCallbacks.Banner.OnAdExpandedEvent -= OnBannerAdExpandedEvent;
            MaxSdkCallbacks.Banner.OnAdCollapsedEvent -= OnBannerAdCollapsedEvent;

            MaxSdkCallbacks.Rewarded.OnAdLoadedEvent -= OnRewardedAdLoadedEvent;
            MaxSdkCallbacks.Rewarded.OnAdLoadFailedEvent -= OnRewardedAdLoadFailedEvent;
            MaxSdkCallbacks.Rewarded.OnAdDisplayedEvent -= OnRewardedAdDisplayedEvent;
            MaxSdkCallbacks.Rewarded.OnAdClickedEvent -= OnRewardedAdClickedEvent;
            MaxSdkCallbacks.Rewarded.OnAdRevenuePaidEvent -= OnRewardedAdRevenuePaidEvent;
            MaxSdkCallbacks.Rewarded.OnAdHiddenEvent -= OnRewardedAdHiddenEvent;
            MaxSdkCallbacks.Rewarded.OnAdDisplayFailedEvent -= OnRewardedAdFailedToDisplayEvent;
            MaxSdkCallbacks.Rewarded.OnAdReceivedRewardEvent -= OnRewardedAdReceivedRewardEvent;

            MaxSdkCallbacks.Interstitial.OnAdRevenuePaidEvent -= OnAdRevenuePaidEvent;
            MaxSdkCallbacks.Rewarded.OnAdRevenuePaidEvent -= OnAdRevenuePaidEvent;
            MaxSdkCallbacks.Banner.OnAdRevenuePaidEvent -= OnAdRevenuePaidEvent;
            MaxSdkCallbacks.MRec.OnAdRevenuePaidEvent -= OnAdRevenuePaidEvent;
        }

        public void OnApplicationPause(bool isPaused)
        {

        }

        public void Init(IAdService manager, BaseAdUnitDefinition definitions)
        {
            _manager = manager;
            _definitions = definitions;

            MaxSdkCallbacks.OnSdkInitializedEvent += (MaxSdkBase.SdkConfiguration sdkConfiguration) =>
            {
                IsInitialized = true;
                // MaxSdk.ShowMediationDebugger();
                #if USING_AMAZON
                // Amazon.EnableLogging(true);
                // Amazon.EnableTesting(true);
                #endif

                MaxSdkCallbacks.Interstitial.OnAdLoadedEvent += OnInterstitialLoadedEvent;
                MaxSdkCallbacks.Interstitial.OnAdLoadFailedEvent += OnInterstitialLoadFailedEvent;
                MaxSdkCallbacks.Interstitial.OnAdDisplayedEvent += OnInterstitialDisplayedEvent;
                MaxSdkCallbacks.Interstitial.OnAdClickedEvent += OnInterstitialClickedEvent;
                MaxSdkCallbacks.Interstitial.OnAdHiddenEvent += OnInterstitialHiddenEvent;
                MaxSdkCallbacks.Interstitial.OnAdDisplayFailedEvent += OnInterstitialAdFailedToDisplayEvent;

                MaxSdkCallbacks.Banner.OnAdLoadedEvent += OnBannerAdLoadedEvent;
                MaxSdkCallbacks.Banner.OnAdLoadFailedEvent += OnBannerAdLoadFailedEvent;
                MaxSdkCallbacks.Banner.OnAdClickedEvent += OnBannerAdClickedEvent;
                MaxSdkCallbacks.Banner.OnAdRevenuePaidEvent += OnBannerAdRevenuePaidEvent;
                MaxSdkCallbacks.Banner.OnAdExpandedEvent += OnBannerAdExpandedEvent;
                MaxSdkCallbacks.Banner.OnAdCollapsedEvent += OnBannerAdCollapsedEvent;

                MaxSdkCallbacks.Rewarded.OnAdLoadedEvent += OnRewardedAdLoadedEvent;
                MaxSdkCallbacks.Rewarded.OnAdLoadFailedEvent += OnRewardedAdLoadFailedEvent;
                MaxSdkCallbacks.Rewarded.OnAdDisplayedEvent += OnRewardedAdDisplayedEvent;
                MaxSdkCallbacks.Rewarded.OnAdClickedEvent += OnRewardedAdClickedEvent;
                MaxSdkCallbacks.Rewarded.OnAdRevenuePaidEvent += OnRewardedAdRevenuePaidEvent;
                MaxSdkCallbacks.Rewarded.OnAdHiddenEvent += OnRewardedAdHiddenEvent;
                MaxSdkCallbacks.Rewarded.OnAdDisplayFailedEvent += OnRewardedAdFailedToDisplayEvent;
                MaxSdkCallbacks.Rewarded.OnAdReceivedRewardEvent += OnRewardedAdReceivedRewardEvent;

                MaxSdkCallbacks.Interstitial.OnAdRevenuePaidEvent += OnAdRevenuePaidEvent;
                MaxSdkCallbacks.Rewarded.OnAdRevenuePaidEvent += OnAdRevenuePaidEvent;
                MaxSdkCallbacks.Banner.OnAdRevenuePaidEvent += OnAdRevenuePaidEvent;
                MaxSdkCallbacks.MRec.OnAdRevenuePaidEvent += OnAdRevenuePaidEvent;
            };

            MaxSdk.SetHasUserConsent(true);
            // MaxSdk.SetIsAgeRestrictedUser(false);
            MaxSdk.SetDoNotSell(false);
            MaxSdk.SetExtraParameter("disable_all_logs", "true");

            // MaxSdk.SetSdkKey(_definitions.GameId);
            // MaxSdk.SetUserId("USER_ID");
            MaxSdk.InitializeSdk();
        }

        private void OnAdRevenuePaidEvent(string adUnitId, MaxSdkBase.AdInfo impressionData)
        {
            double revenue = impressionData.Revenue;
            var impressionParameters = new[]
            {
                new Firebase.Analytics.Parameter("ad_platform", "AppLovin"),
                new Firebase.Analytics.Parameter("ad_source", impressionData.NetworkName),
                new Firebase.Analytics.Parameter("ad_unit_name", impressionData.AdUnitIdentifier),
                new Firebase.Analytics.Parameter("ad_format", impressionData.AdFormat),
                new Firebase.Analytics.Parameter("value", revenue),
                new Firebase.Analytics.Parameter("currency", "USD"), // All AppLovin revenue is sent in USD
            };
            Firebase.Analytics.FirebaseAnalytics.LogEvent("ad_impression", impressionParameters);

            OLogger.Log($"[AppLovin] ImpressionDataReadyEvent, value {revenue}");

            Dictionary<string, string> dic = new Dictionary<string, string>();
            dic.Add("ad_platform", "AppLovin");
            dic.Add("ad_source", impressionData.NetworkName);
            dic.Add("ad_unit_name", impressionData.AdUnitIdentifier);
            dic.Add("ad_format", impressionData.AdFormat);
            dic.Add("currency", "USD");
            dic.Add("value", revenue.ToString());
            AppsFlyerAdRevenue.logAdRevenue("AppLovin", AppsFlyerAdRevenueMediationNetworkType.AppsFlyerAdRevenueMediationNetworkTypeApplovinMax, revenue, "USD", dic);
        }

        public void RequestInterstitial(Action<bool> onLoaded)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] requesting interstitial!");
            _onInterstitialLoaded = onLoaded;

            if (IsInterstitialLoaded() && _onInterstitialLoaded != null)
            {
                _onInterstitialLoaded(true);
            }
            else
            {
                if (_isFirstLoadInterstitial)
                {
                    _isFirstLoadInterstitial = false;

                    #if USING_AMAZON
                    LoadInterstitialByAmazonPublisherService();
                    #endif
                }
                else
                {
                    MaxSdk.LoadInterstitial(_definitions.InterstitialId);
                }
            }
        }

        #if USING_AMAZON
        private void LoadInterstitialByAmazonPublisherService()
        {
            var interstitialAd = new APSInterstitialAdRequest("a31de04e-0ae4-4d80-bfe5-91d076b53b22");
            interstitialAd.onSuccess += (adResponse) =>
            {
                MaxSdk.SetInterstitialLocalExtraParameter(m_Settings.InterstitialId, "amazon_ad_response", adResponse.GetResponse());
                MaxSdk.LoadInterstitial(m_Settings.InterstitialId);
            };
            interstitialAd.onFailedWithError += (adError) =>
            {
                MaxSdk.SetInterstitialLocalExtraParameter(m_Settings.InterstitialId, "amazon_ad_error", adError.GetAdError());
                MaxSdk.LoadInterstitial(m_Settings.InterstitialId);
            };

            interstitialAd.LoadAd();
        }
        #endif

        private void OnInterstitialLoadedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            // Interstitial ad is ready for you to show. MaxSdk.IsInterstitialReady(adUnitId) now returns 'true'
            OLogger.Log($"[{_definitions.ProviderToString}] I got InterstitialAdReadyEvent");

            OLogger.Log($"[{_definitions.ProviderToString}] [Interstitial] Waterfall Name: {adInfo.WaterfallInfo.Name} and Test Name: {adInfo.WaterfallInfo.TestName}");
            OLogger.Log($"[{_definitions.ProviderToString}] [Interstitial] Waterfall latency was: {adInfo.WaterfallInfo.LatencyMillis} milliseconds");

            var waterfallInfoStr = "";
            foreach (var networkResponse in adInfo.WaterfallInfo.NetworkResponses)
            {
                waterfallInfoStr = "Network -> " + networkResponse.MediatedNetwork +
                                   "\n...adLoadState: " + networkResponse.AdLoadState +
                                   "\n...latency: " + networkResponse.LatencyMillis + " milliseconds" +
                                   "\n...credentials: " + networkResponse.Credentials;

                if (networkResponse.Error != null)
                {
                    waterfallInfoStr += "\n...error: " + networkResponse.Error;
                }
            }
            OLogger.Log($"[{_definitions.ProviderToString}] [Interstitial] {waterfallInfoStr}");

            _onInterstitialLoaded?.Invoke(true);

            _manager.HandleInterstitialLoaded();
        }

        private void OnInterstitialLoadFailedEvent(string adUnitId, MaxSdkBase.ErrorInfo errorInfo)
        {
            // Interstitial ad failed to load 
            // AppLovin recommends that you retry with exponentially higher delays, up to a maximum delay (in this case 64 seconds)
            OLogger.LogError($"[{_definitions.ProviderToString}] [Interstitial] I got InterstitialAdLoadFailedEvent, code: " + errorInfo.Code + ", description : " + errorInfo.Message);
            OLogger.LogError($"[{_definitions.ProviderToString}] [Interstitial] Waterfall Name: " + errorInfo.WaterfallInfo.Name + " and Test Name: " + errorInfo.WaterfallInfo.TestName);
            OLogger.LogError($"[{_definitions.ProviderToString}] [Interstitial] Waterfall latency was: " + errorInfo.WaterfallInfo.LatencyMillis + " milliseconds");

            foreach (var networkResponse in errorInfo.WaterfallInfo.NetworkResponses)
            {
                OLogger.LogError($"[{_definitions.ProviderToString}] [Interstitial] Network -> {networkResponse.MediatedNetwork}\n...latency: {networkResponse.LatencyMillis} milliseconds\n...credentials: {networkResponse.Credentials}\n...error: {networkResponse.Error}");
            }

            _onInterstitialLoaded?.Invoke(false);
        }

        private void OnInterstitialDisplayedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] I got InterstitialAdShowSucceededEvent");

            _manager.HandleInterstitialDisplayed();
        }

        private void OnInterstitialAdFailedToDisplayEvent(string adUnitId, MaxSdkBase.ErrorInfo errorInfo, MaxSdkBase.AdInfo adInfo)
        {
            // Interstitial ad failed to display. AppLovin recommends that you load the next ad.
            OLogger.LogError($"[{_definitions.ProviderToString}] I got InterstitialAdShowFailedEvent, code : {errorInfo.Code}, description : {errorInfo.Message}");
            _manager.HandleOnShowInterstitialFailed();
        }

        private void OnInterstitialClickedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] I got InterstitialAdClickedEvent");
            _manager.HandleOnAdsClicked();
        }

        private void OnInterstitialHiddenEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            // Interstitial ad is hidden. Pre-load the next ad.
            OLogger.Log($"[{_definitions.ProviderToString}] I got InterstitialAdClosedEvent");

            _manager.HandleOnClosedInterstitial();
        }

        public bool IsInterstitialLoaded()
        {
            return MaxSdk.IsInterstitialReady(_definitions.InterstitialId);
        }

        public void ShowInterstitial()
        {
            OLogger.Log($"[{_definitions.ProviderToString}] Show Interstitial!");
            if (MaxSdk.IsInterstitialReady(_definitions.InterstitialId))
            {
                MaxSdk.ShowInterstitial(_definitions.InterstitialId);
            }
        }

        public bool IsBannerLoaded()
        {
            return _isBannerLoaded;
        }

        public void RequestBanner(Action<bool> onLoaded)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] requesting banner!");
            _onBannerLoaded = onLoaded;

            if (IsBannerLoaded())
            {
                _onBannerLoaded?.Invoke(true);
            }
            else
            {
                _isBannerLoaded = false;

                #if USING_AMAZON
                CreateAdsByAmazonPublisherService();
                #else
                CreateMaxBannerAd();
                #endif
            }
        }

        #if USING_AMAZON
        private void CreateAdsByAmazonPublisherService()
        {
            int width;
            int height;
            string slotId;
            if (MaxSdkUtils.IsTablet())
            {
                width = 728;
                height = 90;
                slotId = "73e22d20-3401-45fa-9ab0-651d48983161";
            }
            else
            {
                width = 320;
                height = 50;
                slotId = "f372fb3d-a0a1-4bd4-bd74-6e8ba175f544";
            }

            var apsBanner = new APSBannerAdRequest(width, height, slotId);
            apsBanner.onSuccess += (adResponse) =>
            {
                MaxSdk.SetBannerLocalExtraParameter(m_Settings.BannerId, "amazon_ad_response", adResponse.GetResponse());
                CreateMaxBannerAd();
            };
            apsBanner.onFailedWithError += (adError) =>
            {
                MaxSdk.SetBannerLocalExtraParameter(m_Settings.BannerId, "amazon_ad_error", adError.GetAdError());
                CreateMaxBannerAd();
            };

            apsBanner.LoadAd();
        }
        #endif

        private void CreateMaxBannerAd()
        {
            // Banners are automatically sized to 320×50 on phones and 728×90 on tablets
            // You may call the utility method MaxSdkUtils.isTablet() to help with view sizing adjustments
            MaxSdk.CreateBanner(_definitions.BannerId, MaxSdkBase.BannerPosition.BottomCenter);
            MaxSdk.SetBannerExtraParameter(_definitions.BannerId, "adaptive_banner", "true");

            // Set background or background color for banners to be fully functional
            MaxSdk.SetBannerBackgroundColor(_definitions.BannerId, new Color(1f, 1f, 1f, 0f));
        }

        public void ShowBanner()
        {
            MaxSdk.ShowBanner(_definitions.BannerId);
        }

        public void HideBanner()
        {
            MaxSdk.HideBanner(_definitions.BannerId);
        }
        
        public void DestroyBanner()
        {
            MaxSdk.DestroyBanner(_definitions.BannerId);
        }

        public void StartBannerAutoRefresh()
        {
            MaxSdk.StartBannerAutoRefresh(m_Settings.BannerId);
        }
        
        public void StopBannerAutoRefresh()
        {
            MaxSdk.StopBannerAutoRefresh(m_Settings.BannerId);
        }

        private void OnBannerAdLoadedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] [Banner] Waterfall Name: {adInfo.WaterfallInfo.Name} and Test Name: {adInfo.WaterfallInfo.TestName}");
            OLogger.Log($"[{_definitions.ProviderToString}] [Banner] Waterfall latency was: {adInfo.WaterfallInfo.LatencyMillis} milliseconds");

            string waterfallInfoStr = "";
            foreach (var networkResponse in adInfo.WaterfallInfo.NetworkResponses)
            {
                waterfallInfoStr = "Network -> " + networkResponse.MediatedNetwork +
                                   "\n...adLoadState: " + networkResponse.AdLoadState +
                                   "\n...latency: " + networkResponse.LatencyMillis + " milliseconds" +
                                   "\n...credentials: " + networkResponse.Credentials;

                if (networkResponse.Error != null)
                {
                    waterfallInfoStr += "\n...error: " + networkResponse.Error;
                }
            }

            OLogger.Log($"[{_definitions.ProviderToString}] [Banner] {waterfallInfoStr}");
            OLogger.Log($"[{_definitions.ProviderToString}] Banner loaded!");
            _onBannerLoaded?.Invoke(true);
            _isBannerLoaded = true;

            _manager.HandleBannerLoaded(true);
        }

        private void OnBannerAdLoadFailedEvent(string adUnitId, MaxSdkBase.ErrorInfo errorInfo)
        {
            OLogger.LogError($"[{_definitions.ProviderToString}] Banner failed to load with code: {errorInfo.Code}, description: {errorInfo.Message}");
            OLogger.LogError($"[{_definitions.ProviderToString}] [Banner] Waterfall Name: {errorInfo.WaterfallInfo.Name} and Test Name: {errorInfo.WaterfallInfo.TestName}");
            OLogger.LogError($"[{_definitions.ProviderToString}] [Banner] Waterfall latency was: {errorInfo.WaterfallInfo.LatencyMillis} milliseconds");

            foreach (var networkResponse in errorInfo.WaterfallInfo.NetworkResponses)
            {
                OLogger.LogError($"[{_definitions.ProviderToString}] [Banner] Network -> {networkResponse.MediatedNetwork}" +
                      $"\n...latency: {networkResponse.LatencyMillis} milliseconds" +
                      $"\n...credentials: {networkResponse.Credentials}" +
                      $"\n...error: {networkResponse.Error}");
            }

            _onBannerLoaded?.Invoke(false);
            _isBannerLoaded = false;

            _manager.HandleBannerLoaded(false);
        }

        private void OnBannerAdClickedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] Banner clicked");
            _manager.HandleOnAdsClicked();
        }

        private void OnBannerAdRevenuePaidEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] Banner AdRevenuePaidEvent");
        }

        private void OnBannerAdExpandedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] Banner AdExpandedEvent");
        }

        private void OnBannerAdCollapsedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] Banner AdCollapsedEvent");
        }

        public void RequestRewardedVideo(Action<bool> onLoaded)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] requesting rewarded video!");
            _onVideoRewardedLoaded = onLoaded;

            if (IsRewardedVideoLoaded() && _onVideoRewardedLoaded != null)
            {
                _onVideoRewardedLoaded(true);
            }
            else
            {
                MaxSdk.LoadRewardedAd(_definitions.RewardedVideoId);
            }
        }

        public void ShowRewardedVideo()
        {
            OLogger.Log($"[{_definitions.ProviderToString}] Show Rewarded Video!");
            if (IsRewardedVideoLoaded())
            {
                _hasRewarded = false;
                MaxSdk.ShowRewardedAd(_definitions.RewardedVideoId);
            }
        }

        public bool IsRewardedVideoLoaded()
        {
            return MaxSdk.IsRewardedAdReady(_definitions.RewardedVideoId);
        }

        private void OnRewardedAdLoadedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            // Rewarded ad is ready for you to show. MaxSdk.IsRewardedAdReady(adUnitId) now returns 'true'.

            OLogger.Log($"[{_definitions.ProviderToString}] I got OnRewardedAdLoadedEvent");
            _onVideoRewardedLoaded?.Invoke(true);

            _manager.HandleRewardedVideoLoaded();
        }

        private void OnRewardedAdLoadFailedEvent(string adUnitId, MaxSdkBase.ErrorInfo errorInfo)
        {
            // Rewarded ad failed to load 
            // AppLovin recommends that you retry with exponentially higher delays, up to a maximum delay (in this case 64 seconds).

            OLogger.LogError($"[{_definitions.ProviderToString}] I got OnRewardedAdLoadFailedEvent, code : {errorInfo.Code}, description : {errorInfo.Message}");
            _onVideoRewardedLoaded?.Invoke(false);
            _hasRewarded = false;
        }

        private void OnRewardedAdDisplayedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] I got OnRewardedAdDisplayedEvent");

            _manager.HandleRewardedVideoDisplayed();
        }

        private void OnRewardedAdFailedToDisplayEvent(string adUnitId, MaxSdkBase.ErrorInfo errorInfo, MaxSdkBase.AdInfo adInfo)
        {
            // Rewarded ad failed to display. AppLovin recommends that you load the next ad.
            OLogger.Log($"[{_definitions.ProviderToString}] I got OnRewardedAdFailedToDisplayEvent");
            _hasRewarded = false;
        }

        private void OnRewardedAdClickedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] I got OnRewardedAdClickedEvent, name = {adInfo.DspName}");
            _manager.HandleOnAdsClicked();
        }

        private void OnRewardedAdHiddenEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            // Rewarded ad is hidden. Pre-load the next ad
            OLogger.Log($"[{_definitions.ProviderToString}] I got OnRewardedAdHiddenEvent");
            _manager.HandleOnWatchVideoReward(_hasRewarded);
        }

        private void OnRewardedAdReceivedRewardEvent(string adUnitId, MaxSdk.Reward reward, MaxSdkBase.AdInfo adInfo)
        {
            // The rewarded ad displayed and the user should receive the reward.
            OLogger.Log($"[{_definitions.ProviderToString}] I got OnRewardedAdReceivedRewardEvent, amount = {reward.Amount} name = {reward.Label}");
            _hasRewarded = true;
        }

        private void OnRewardedAdRevenuePaidEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            // Ad revenue paid. Use this callback to track user revenue.
        }

        public void RequestAppOpenAd(Action<bool> onLoaded)
        {
            throw new NotImplementedException();
        }

        public bool IsAppOpenAdLoaded()
        {
            throw new NotImplementedException();
        }

        public void ShowAppOpenAd()
        {
            throw new NotImplementedException();
        }
    }
}
#endif
