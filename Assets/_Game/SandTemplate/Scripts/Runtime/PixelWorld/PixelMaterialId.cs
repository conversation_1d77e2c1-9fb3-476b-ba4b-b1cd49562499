namespace OP.BlockSand
{
    /// <summary>
    /// These are for convenience so you can easily fetch materials from the list by id.
    /// </summary>
    public enum PixelMaterialId
    {
        Empty = 0
        ,Static = 1
        ,Rock = 3
        ,Sand = 4
        ,Water = 5
        ,Wood = 6
        ,Oil = 7
        ,Acid = 8
        ,Lava = 9
        ,Snow = 10
        ,Steam = 11,

        // Add you own types here:
        
        BlockClearing = 50,
        
        BlockSand_0 = 100,
        BlockSand_1 = 101,
        BlockSand_2 = 102,
        BlockSand_3 = 103,
        BlockSand_4 = 104,
        BlockSand_5 = 105,
        BlockSand_6 = 106,
        BlockSand_7 = 107,
    }
}
