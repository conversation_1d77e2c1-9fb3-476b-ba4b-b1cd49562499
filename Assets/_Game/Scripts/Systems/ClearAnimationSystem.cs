using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Unity.Collections;
using UnityEngine.Serialization;

namespace OP.BlockSand
{
    /// <summary>
    /// System to handle the clear animation for sand clusters
    /// Changes pixel colors to white, then back to original, then clears them
    /// </summary>
    public class ClearAnimationSystem : MonoBehaviour
    {
        [Header("Animation Settings")] 
        [SerializeField] private float _animationDuration = 0.5f;
        [SerializeField] private float _whiteFlashDuration = 0.15f;
        [SerializeField] private int _flashCount = 3;
        [SerializeField] private Color32 _flashColor = Color.white;

        private PixelWorld _pixelWorld;
        private PixelMaterials _materialDefinition;
        private readonly List<ClearAnimationData> _animatingPixels = new();

        public bool IsAnimating { get; private set; } = false;

        public void Initialize(PixelWorld pixelWorld, PixelMaterials materialDefinition)
        {
            _pixelWorld = pixelWorld;
            _materialDefinition = materialDefinition;
        }

        /// <summary>
        /// Start clear animation for a cluster of pixels
        /// </summary>
        public void StartClearAnimation(List<Vector2Int> pixelPositions, System.Action onComplete = null)
        {
            if (IsAnimating)
            {
                Debug.LogWarning("Clear animation already in progress!");
                return;
            }

            _animatingPixels.Clear();

            // Collect pixel data and stop their free falling
            foreach (var pos in pixelPositions)
            {
                if (_pixelWorld.TryGetPixelAt(pos.x, pos.y, out Pixel pixel))
                {
                    // Stop free falling during animation
                    pixel.StopFreeFalling();

                    // Get material ID from material index for drawing
                    var materialId = GetMaterialIdFromIndex(pixel.materialIndex);
                    _pixelWorld.DrawPixelAt(pos.x, pos.y, pixel.r, pixel.g, pixel.b, pixel.a, materialId);

                    var animData = new ClearAnimationData(pos,
                        new Color32(pixel.r, pixel.g, pixel.b, pixel.a),
                        materialId);
                    _animatingPixels.Add(animData);
                }
            }

            if (_animatingPixels.Count > 0)
            {
                StartCoroutine(AnimateClearSequence(onComplete));
            }
            else
            {
                onComplete?.Invoke();
            }
        }

        private IEnumerator AnimateClearSequence(System.Action onComplete)
        {
            IsAnimating = true;

            float flashInterval = _animationDuration / (_flashCount * 2); // *2 for on/off cycles

            // Flash animation: alternate between original color and white
            for (int flash = 0; flash < _flashCount; flash++)
            {
                // Flash to white
                SetPixelsColor(_flashColor);
                yield return new WaitForSeconds(_whiteFlashDuration);

                // Flash back to original
                SetPixelsToOriginalColor();
                yield return new WaitForSeconds(flashInterval - _whiteFlashDuration);
            }

            // Final flash to white before clearing
            SetPixelsColor(_flashColor);
            yield return new WaitForSeconds(_whiteFlashDuration);

            // Clear all pixels
            ClearAllAnimatingPixels();

            IsAnimating = false;
            _animatingPixels.Clear();

            onComplete?.Invoke();
        }

        private void SetPixelsColor(Color32 color)
        {
            foreach (var animData in _animatingPixels)
            {
                _pixelWorld.DrawPixelForAnimation(animData.position.x, animData.position.y,
                    color.r, color.g, color.b, color.a, animData.materialId);
            }
        }

        private void SetPixelsToOriginalColor()
        {
            foreach (var animData in _animatingPixels)
            {
                var originalColor = animData.originalColor;
                _pixelWorld.DrawPixelForAnimation(animData.position.x, animData.position.y,
                    originalColor.r, originalColor.g, originalColor.b, originalColor.a,
                    animData.materialId);
            }
        }

        private void ClearAllAnimatingPixels()
        {
            foreach (var animData in _animatingPixels)
            {
                _pixelWorld.DrawPixelAt(animData.position.x, animData.position.y,
                    PixelMaterialId.Empty);
            }
        }

        /// <summary>
        /// Stop current animation and clear all pixels immediately
        /// </summary>
        public void StopAnimation()
        {
            if (IsAnimating)
            {
                StopAllCoroutines();
                ClearAllAnimatingPixels();
                IsAnimating = false;
                _animatingPixels.Clear();
            }
        }

        /// <summary>
        /// Get all pixel positions currently being animated
        /// </summary>
        public List<Vector2Int> GetAnimatingPixelPositions()
        {
            var positions = new List<Vector2Int>();
            foreach (var animData in _animatingPixels)
            {
                positions.Add(animData.position);
            }

            return positions;
        }

        /// <summary>
        /// Convert material index to material ID using the pixel world's materials
        /// </summary>
        private PixelMaterialId GetMaterialIdFromIndex(int materialIndex)
        {
            if (materialIndex >= 0 && materialIndex < _materialDefinition.Materials.Length)
                return _materialDefinition.Materials[materialIndex].id;
            
            OLogger.LogError($"Invalid material index {materialIndex}!");
            return PixelMaterialId.Empty;
        }
    }
}