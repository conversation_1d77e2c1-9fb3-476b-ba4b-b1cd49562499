using System.Collections.Generic;
using OnePuz.Data;
using Sirenix.OdinInspector;
using UnityEngine;

namespace OnePuz.UI
{
    [CreateAssetMenu(fileName = "UIPrefabDefinitions", menuName = "OnePuz/UIPrefabDefinitions")]
    public class UIPrefabDefinitions : ScriptableObject
    {
        [TableList(AlwaysExpanded = false, DrawScrollView = false, ShowPaging = true, NumberOfItemsPerPage = 5), Space]
        public List<Datum> Global = new List<Datum>();
        [TableList(AlwaysExpanded = false, DrawScrollView = false, ShowPaging = true, NumberOfItemsPerPage = 5), Space]
        public List<Datum> Loading = new List<Datum>();
        [TableList(AlwaysExpanded = false, DrawScrollView = false, ShowPaging = true, NumberOfItemsPerPage = 5), Space]
        public List<Datum> Home = new List<Datum>();
        [TableList(AlwaysExpanded = false, DrawScrollView = false, ShowPaging = true, NumberOfItemsPerPage = 5), Space]
        public List<Datum> Game = new List<Datum>();

        [System.Serializable]
        public class Datum
        {
            [SerializeField, VerticalGroup("Define"), GUIColor("cyan"), PanelKey] 
            private string _id = UIKeys.Panel.NONE;

            public string Id => _id;

            [VerticalGroup("Define")]
            public PanelLayer Layer = PanelLayer.UI;
            
            private int MaxLayerNumber => Layer == PanelLayer.DEFAULT ? (int)(PanelLayer.UI - 2) : 98;
            
            [PropertyRange(0, "MaxLayerNumber"), SerializeField, VerticalGroup("Define")]
            private int Order = 1;

            public int LayerOrder => (int)Layer + Order;
            
            [VerticalGroup("Define")]
            public bool AlwaysActive = false;
            
            public ResourceAsset PrefabReference;
        }
    }
}