using OnePuz.Definition;
using OnePuz.Services;
using Sirenix.OdinInspector;
using UnityEngine;

namespace OnePuz.Data
{
    [System.Serializable]
    public class RewardData
    {
        [ReadOnly]
        [SerializeField]
        private string _id;

        public string Id => _id;

        [OnValueChanged("UpdateId")]
        public RewardType RewardType;

        [SerializeField]
        [ShowIf("RewardType", RewardType.CURRENCY)]
        [OnValueChanged("UpdateId")]
        private CurrencyType _currencyType;

        public CurrencyType CurrencyType
        {
            get => _currencyType;
            set => _currencyType = value;
        }

        [SerializeField]
        [ShowIf("RewardType", RewardType.BOOSTER)]
        [OnValueChanged("UpdateId")]
        private BoosterType _boosterType;

        public BoosterType BoosterType
        {
            get => _boosterType;
            set => _boosterType = value;
        }

#if USE_SKIN_SHOP
        [SerializeField]
        [ShowIf("RewardType", RewardType.Skin)]
        [OnValueChanged("UpdateId")]
        private SkinType _skinType;
        public SkinType SkinType
        {
            get => _skinType;
            set => _skinType = value;
        }
        
        [SerializeField]
        [ShowIf("RewardType", RewardType.Skin)]
        [OnValueChanged("UpdateId")]
        private int _skinId;
        public int SkinId
        {
            get => _skinId;
            set => _skinId = value;
        }
#endif

        public int Quantity = 1;
        
        public static RewardData InfiniteLive(int duration)
        {
            return new RewardData(RewardType.INFINITE_LIVE, duration);
        }
        
        public static RewardData NoAds()
        {
            return new RewardData(RewardType.NO_ADS, 1);
        }
        
        public static RewardData Booster(BoosterType boosterType, int quantity)
        {
            return new RewardData(boosterType, quantity);
        }
        
        public static RewardData Currency(CurrencyType currencyType, int quantity)
        {
            return new RewardData(currencyType, quantity);
        }

        public RewardData()
        {
            RewardType = RewardType.CURRENCY;
            _currencyType = CurrencyType.COIN;
            Quantity = 0;
            UpdateId();
        }

        public RewardData(BoosterType boosterType, int quantity) : this()
        {
            RewardType = RewardType.BOOSTER;
            BoosterType = boosterType;
            Quantity = quantity;
        }
        
        public RewardData(CurrencyType currencyType, int quantity) : this()
        {
            RewardType = RewardType.CURRENCY;
            CurrencyType = currencyType;
            Quantity = quantity;
        }
        
        public RewardData(RewardType rewardType, int quantity) : this()
        {
            RewardType = rewardType;
            Quantity = quantity;
        }

        private void UpdateId()
        {
            _id = $"{RewardType}_{GetRewardSubType()}";
        }

        private string GetRewardSubType()
        {
            return RewardType switch
            {
                RewardType.CURRENCY => _currencyType.ToString(),
                RewardType.BOOSTER => _boosterType.ToString(),
#if USE_SKIN_SHOP
                RewardType.Skin => _skinType.ToString(),
#endif
                _ => ""
            };
        }
    }
}