using System.Linq;
using Sirenix.OdinInspector;
using UnityEngine;
using Random = UnityEngine.Random;

namespace OP.BlockSand
{
    public class ShapeSpawner : MonoBehaviour
    {
        [SerializeField]
        private ShapeDefinition _shapeDefinition;
        
        [SerializeField]
        private Transform[] _spawnPoints;
        
        private readonly Shape[] _shapes = new Shape[3];

        public void Init()
        {
            SpawnAllShapes();
        }
        
        [Button]
        public void SpawnAllShapes()
        {
            foreach (var shape in _shapes)
            {
                if (!shape) continue;
                
                Core.ScenePool.Recycle(shape.gameObject);
            }
            
            var blockTypes = Enumerable.Range(100, 3).OrderBy(t => Random.value).Take(2).ToList();
            for (var i = 0; i < _spawnPoints.Length; i++)
            {
                _shapes[i] = SpawnShape(i, (BlockType)blockTypes[Random.Range(0, 2)]);
            }
        }

        private Shape SpawnShape(int index, BlockType blockType)
        {
            var shape = Core.ScenePool.Spawn(_shapeDefinition.shapePrefab.gameObject).GetComponent<Shape>();
            shape.OTransform.localPosition = _spawnPoints[index].position;
            shape.Init(_shapeDefinition.shapes[index], blockType);
            return shape;
        }

        public Shape GetShape(int index)
        {
            return _shapes[index];
        }
        
        public bool AnyShapesLeft()
        {
            return _shapes.Any(t => t);
        }

        public void RemoveShape(int index)
        {
            if (_shapes[index])
            {
                _shapes[index] = null;
            }
        }

        public void Despawn(Shape shape)
        {
            shape.Reset();
            Core.ScenePool.Recycle(shape.gameObject);
        }
        
        public void Reset()
        {
            foreach (var shape in _shapes)
            {
                if (!shape) continue;
                
                Core.ScenePool.Recycle(shape.gameObject);
            }
        }
    }
}