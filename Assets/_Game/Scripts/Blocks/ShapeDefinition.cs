using System.Collections.Generic;
using Sirenix.OdinInspector;
using UnityEngine;

namespace OP.BlockSand
{
    [CreateAssetMenu(fileName = "ShapeDefinitions.asset", menuName = "Game/ShapeDefinitions")]
    public class ShapeDefinition : ScriptableObject
    {
        public GameObject shapePrefab;
        
        public List<Datum> shapes;
        
        [System.Serializable]
        public class Datum
        {
            [Title("Shape Configuration")]
            [LabelWidth(60)]
            public string id;

            [Space(5)]
            [ReadOnly]
            public Vector2Int size;

            [Space(5)]
            [ReadOnly]
            [ListDrawerSettings(ShowIndexLabels = false, DraggableItems = false, ShowPaging = false)]
            public List<Vector2Int> coordinates;
        }
    }
}