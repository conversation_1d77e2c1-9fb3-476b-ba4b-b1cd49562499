using System;
using System.Collections.Generic;
using PrimeTween;
using Sirenix.OdinInspector;
using UnityEngine;

namespace OP.BlockSand
{
    public class Shape : MonoBehaviour
    {
        public struct BlockPixel
        {
            public Vector2Int coordinate;
            public BlockType blockType;
            public Color32 color;
        }

        public const int BlockNormalSortOrder = 5;
        public const int BlockSelectedSortOrder = 10;

        [SerializeField]
        private GameObject _elementPrefab;

        [SerializeField]
        private BlockDefinitions _blockDefinitions;

        [SerializeField]
        private AnimationCurve _moveUpOffsetCurve;

        private Transform _transform;
        public Transform OTransform => _transform ??= transform;

        private readonly List<Vector2Int> _coordinates = new();
        private readonly List<BlockElement> _elements = new();

        [SerializeField, ReadOnly]
        private Rect _rect;

        public Rect Rect => _rect;

        private Vector3 _originalPosition;

        private bool _handMoving;
        private bool _shouldMove;
        private bool _handReleased;
        private bool _shouldMoveBack;
        private bool _shouldBePlaced;
        private Vector3 _lastTargetPosition;
        private Vector3 _targetPosition;

        private Action _onPlacedShape;

        // All pixels, coordinates from bottom left
        private readonly List<BlockPixel> _pixels = new();

        public void Init(ShapeDefinition.Datum datum, BlockType blockType)
        {
            _coordinates.Clear();
            _coordinates.AddRange(datum.coordinates);
            _rect = CalculateRect();
            _originalPosition = transform.position;

            _elements.Clear();

            foreach (var coordinate in _coordinates)
            {
                var element = Core.ScenePool.Spawn(_elementPrefab, transform).GetComponent<BlockElement>();
                element.Init(blockType, coordinate, _blockDefinitions.GetRandomTexture(blockType));
                element.UpdateSortOrder(BlockNormalSortOrder);
                element.transform.localPosition = new Vector2(coordinate.x - datum.size.x / 2f + 0.5f, coordinate.y - datum.size.y / 2f + 0.5f) * _blockDefinitions.blockSize;

                _elements.Add(element);
            }

            UpdatePixels();

            transform.localScale = Vector3.zero;
            AnimateScale(0.8f, 0.2f);
        }

        private void LateUpdate()
        {
            if (!_shouldMove)
                return;

            var speed = 150f;
            if (_handReleased)
            {
                if (_shouldBePlaced)
                    speed = 6f;
                else if (_shouldMoveBack)
                    speed = 60f;
                else
                    speed = 3f;
            }
            else if (!_handMoving)
                speed = 60f;

            var delta = speed * Time.deltaTime;
            var sqrDistance = Vector3.SqrMagnitude(_targetPosition - OTransform.position);

            OTransform.position = Vector3.MoveTowards(OTransform.position, _targetPosition, delta);

            if (sqrDistance <= 0.4f && _shouldMoveBack)
            {
                OTransform.position = _targetPosition;
                _shouldMove = false;
                _shouldMoveBack = false;
            }

            if (sqrDistance <= 0.05f && _shouldBePlaced)
            {
                _shouldBePlaced = false;
                _onPlacedShape?.Invoke();
            }
        }

        public void HandleSelected(Vector3 worldPos, Rect boardRect)
        {
            _shouldMoveBack = false;
            _handMoving = false;
            _handReleased = false;
            _shouldBePlaced = false;

            _targetPosition = worldPos;
            _targetPosition.y += _moveUpOffsetCurve.Evaluate((worldPos.y - _originalPosition.y) / 5f);
            _targetPosition = ClampPosition(_targetPosition, boardRect);
            _lastTargetPosition = _targetPosition;

            _shouldMove = true;

            AnimateScale(1, 0.15f);
            UpdateBlockSortOrder(BlockSelectedSortOrder);
        }

        public void HandleMoving(Vector3 worldPos, Rect boardRect)
        {
            _targetPosition = worldPos;
            _targetPosition.y += _moveUpOffsetCurve.Evaluate((worldPos.y - _originalPosition.y) / 5f);
            _targetPosition = ClampPosition(_targetPosition, boardRect);

            if (Vector3.Distance(_targetPosition, _lastTargetPosition) < 0.2f && !_handMoving)
            {
                _shouldMove = true;
            }
            else
            {
                if (!_handMoving)
                    AnimateScale(1, 0.05f);

                _handMoving = true;

                _shouldMove = true;
            }
        }

        private Vector3 ClampPosition(Vector3 targetWorldPosition, Rect boardRect)
        {
            var clampedPosition = targetWorldPosition;
            clampedPosition.x = Mathf.Clamp(clampedPosition.x, boardRect.xMin + _rect.width / 2, boardRect.xMax - _rect.width / 2);
            clampedPosition.y = Mathf.Clamp(clampedPosition.y, float.MinValue, boardRect.yMax - _rect.height / 2);

            return clampedPosition;
        }

        public void HandleRelease()
        {
            _handReleased = true;

            UpdateBlockSortOrder(BlockNormalSortOrder);
        }

        public void MoveToTarget(Vector3 targetPos, Action onPlaced)
        {
            _targetPosition = targetPos;
            _shouldBePlaced = true;
            _onPlacedShape = onPlaced;
        }

        public void MoveBack()
        {
            _shouldMoveBack = true;
            _targetPosition = _originalPosition;

            AnimateScale(0.8f, 0.2f);
        }

        private void AnimateScale(float blockScale, float duration, Action onComplete = null)
        {
            Tween.Scale(OTransform, blockScale, duration).OnComplete(() => onComplete?.Invoke());
        }

        private void UpdateBlockSortOrder(int value)
        {
            foreach (var element in _elements)
            {
                element.UpdateSortOrder(value);
            }
        }

        private void UpdatePixels()
        {
            _pixels.Clear();

            foreach (var element in _elements)
            {
                var elementPixels = element.GetPixels();
                for (var i = 0; i < elementPixels.Length; i++)
                {
                    var xInt = i % BlockElement.PixelNumberSize + element.Coordinate.x * BlockElement.PixelNumberSize;
                    var yInt = i / BlockElement.PixelNumberSize + element.Coordinate.y * BlockElement.PixelNumberSize;
                    _pixels.Add(new BlockPixel()
                    {
                        coordinate = new Vector2Int(xInt, yInt),
                        blockType = element.BlockType,
                        color = elementPixels[i]
                    });
                }
            }
        }

        public List<BlockPixel> GetPixels() => _pixels;

        private Rect CalculateRect()
        {
            var minX = float.MaxValue;
            var maxX = float.MinValue;
            var minY = float.MaxValue;
            var maxY = float.MinValue;

            var blockSize = _blockDefinitions.blockSize;
            var halfBlockSize = blockSize / 2f;
            foreach (var coordinate in _coordinates)
            {
                var position = (Vector2)coordinate * blockSize;
                minX = Mathf.Min(minX, position.x - halfBlockSize);
                maxX = Mathf.Max(maxX, position.x + halfBlockSize);
                minY = Mathf.Min(minY, position.y - halfBlockSize);
                maxY = Mathf.Max(maxY, position.y + halfBlockSize);
            }

            return new Rect(minX, minY, maxX - minX, maxY - minY);
        }

        public void Reset()
        {
            _shouldMove = false;
            _shouldMoveBack = false;

            foreach (var element in _elements)
            {
                Core.ScenePool.Recycle(element.gameObject);
            }
        }
    }
}