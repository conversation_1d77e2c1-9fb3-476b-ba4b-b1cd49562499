using Unity.Burst;
using Unity.Collections;
using Unity.Jobs;

namespace OP.BlockSand
{
    /// <summary>
    /// Job to check if the game should end (lose condition)
    /// Checks if any sleeping pixels are at row 90 or above
    /// </summary>
    [BurstCompile]
    public struct LoseCheckJob : IJob
    {
        // Input data
        [ReadOnly] public int worldWidth;
        [ReadOnly] public int worldHeight;
        [ReadOnly] public int loseThresholdY; // Should be 90
        [ReadOnly] public NativeArray<Pixel> pixelBuffer;
        
        // Output data
        public NativeArray<bool> hasLost; // Single bool result
        public NativeArray<int> highestSleepingPixelY; // For debugging/info
        
        public void Execute()
        {
            bool gameHasLost = false;
            int highestY = -1;
            
            // Check from top to bottom for optimization
            // Start from the lose threshold and work upwards
            for (int y = worldHeight - 1; y >= loseThresholdY; y--)
            {
                for (int x = 0; x < worldWidth; x++)
                {
                    int index = GetIndex(x, y);
                    var pixel = pixelBuffer[index];
                    
                    // Check if pixel is not empty and is sleeping (settled)
                    if (!pixel.IsEmpty() && pixel.IsAsleep())
                    {
                        gameHasLost = true;
                        highestY = y;
                        
                        // Early exit since we found a losing condition
                        hasLost[0] = gameHasLost;
                        highestSleepingPixelY[0] = highestY;
                        return;
                    }
                }
            }
            
            // If we get here, no losing condition was found
            hasLost[0] = gameHasLost;
            highestSleepingPixelY[0] = highestY;
        }
        
        private int GetIndex(int x, int y)
        {
            return y * worldWidth + x;
        }
    }
    
    /// <summary>
    /// Optimized version that checks from top down and can early exit
    /// </summary>
    [BurstCompile]
    public struct OptimizedLoseCheckJob : IJob
    {
        // Input data
        [ReadOnly] public int worldWidth;
        [ReadOnly] public int worldHeight;
        [ReadOnly] public int loseThresholdY; // Should be 90
        [ReadOnly] public NativeArray<Pixel> pixelBuffer;
        
        // Output data
        public NativeArray<bool> hasLost;
        public NativeArray<int> firstLosePixelX;
        public NativeArray<int> firstLosePixelY;
        
        public void Execute()
        {
            // Start checking from the top of the world downwards
            // This is more efficient as we can early exit when we find the first non-empty row
            for (int y = worldHeight - 1; y >= 0; y--)
            {
                bool hasPixelsInThisRow = false;
                
                // Check if this row has any non-empty pixels
                for (int x = 0; x < worldWidth; x++)
                {
                    int index = GetIndex(x, y);
                    var pixel = pixelBuffer[index];
                    
                    if (!pixel.IsEmpty())
                    {
                        hasPixelsInThisRow = true;
                        
                        // If this pixel is sleeping and we're at or above the lose threshold
                        if (pixel.IsAsleep() && y >= loseThresholdY)
                        {
                            hasLost[0] = true;
                            firstLosePixelX[0] = x;
                            firstLosePixelY[0] = y;
                            return;
                        }
                    }
                }
                
                // If we found pixels in this row but we're below the lose threshold,
                // we can stop checking as all pixels below will also be below the threshold
                if (hasPixelsInThisRow && y < loseThresholdY)
                {
                    break;
                }
            }
            
            // No losing condition found
            hasLost[0] = false;
            firstLosePixelX[0] = -1;
            firstLosePixelY[0] = -1;
        }
        
        private int GetIndex(int x, int y)
        {
            return y * worldWidth + x;
        }
    }
}
