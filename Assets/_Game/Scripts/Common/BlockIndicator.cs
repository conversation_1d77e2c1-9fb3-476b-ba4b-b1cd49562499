using System;
using System.Collections.Generic;
using System.Linq;
using _FeatureHub.Attributes.Core;
using _FeatureHub.Utilities;
using OnePuz.Extensions;
using PrimeTween;
using Sirenix.OdinInspector;
using Spine.Unity;
using TMPro;
using UnityEngine;

namespace _Main.Scripts.Common
{
    [ReferenceInBackground]
    public class BlockIndicator : MonoBehaviour
    {
        [SerializeField, ReferenceValue]
        private Canvas m_Canvas;

        [SerializeField, ReferenceValue("PanelToast/ToastContainer"), FoldoutGroup("Toast")]
        private RectTransform m_ToastContainer;
        
        [SerializeField, ReferenceValue("PanelToast/Label"), FoldoutGroup("Toast")]
        private TMP_Text m_LabelToast;
        
        private static BlockIndicator m_Instance;

        private ToastPooling m_ToastPooling;

        private static void SafeInitialize()
        {
            if (m_Instance is not null)
                return;
            m_Instance = Instantiate(Resources.Load<BlockIndicator>("BlockIndicator"));
            
            m_Instance.m_Canvas.enabled = false;
            m_Instance.m_ToastPooling = new ToastPooling(m_Instance.m_ToastContainer);
            
            DontDestroyOnLoad(m_Instance.gameObject);
        }

        private void OnDestroy() => m_Instance = null;

        public static void Toast(string text)
        {
            SafeInitialize();
            var toast = m_Instance.m_ToastPooling.Get(text);
            if(toast is null)
                return;
    
            m_Instance.m_Canvas.enabled = true;
            
            toast.root.anchoredPosition = Vector2.zero;
            toast.root.SetActive(true);
            toast.root.SetAsLastSibling();
            toast.animationSequence = Sequence.Create()
                .Group(Tween.Alpha(toast.canvasGroup, 0f, 1f, 0.25f))
                .Group(Tween.Scale(toast.root, new Vector3(0.8f, 0), Vector3.one, 0.25f, Ease.OutBack))
                .Chain(Tween.UIAnchoredPositionY(toast.root, 0, 200, 0.75f, Ease.InCubic))
                .Group(Tween.Alpha(toast.canvasGroup, 1f, 0f, 0.75f, Ease.InCirc))
                .OnComplete(m_Instance, @this => @this.HandleToastCompleted(toast));
        }

        private void HandleToastCompleted(ToastPoolMember toast)
        {
            toast.root.SetActive(false);
            m_Instance.m_ToastPooling.activeKeys.Dequeue();
            if(m_ToastPooling.poolMembers.Any(entry => entry.animationSequence.isAlive))
                return;
            m_Canvas.enabled = false;
        }
        
        private readonly struct ToastPooling
        {
            private readonly RectTransform prefab;
            public readonly Queue<string> activeKeys;
            public readonly List<ToastPoolMember> poolMembers;
            
            public ToastPooling(RectTransform toastContainer)
            {
                prefab = toastContainer;
                activeKeys = new Queue<string>();
                poolMembers = new List<ToastPoolMember> { new(prefab) };
                poolMembers[0].root.SetActive(false);
            }

            public ToastPoolMember Get(string text)
            {
                if (!activeKeys.Contains(text))
                {
                    var result = poolMembers.FirstOrDefault(entry => !entry.root.ActiveSelf());
                    if (result == null)
                    {
                        result = new ToastPoolMember(Instantiate(prefab, prefab.parent));
                        poolMembers.Add(result);
                    }

                    result.labelToast.text = text;
                    activeKeys.Enqueue(text);
                    return result;
                }
                return null;
            }
        }
        
        private class ToastPoolMember
        {
            public readonly RectTransform root;
            public readonly CanvasGroup canvasGroup;
            public readonly TMP_Text labelToast;

            public Sequence animationSequence;
            
            public ToastPoolMember(RectTransform root)
            {
                this.root = root;
                canvasGroup = root.GetComponent<CanvasGroup>();
                labelToast = root.FindDeep<TMP_Text>("Label");
            }
        }
    }
}