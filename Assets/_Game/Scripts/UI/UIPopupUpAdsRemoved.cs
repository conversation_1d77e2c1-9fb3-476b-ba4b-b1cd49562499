using System.Collections;
using System.Collections.Generic;
using System.Threading;
using _FeatureHub.PrimeTweenExtra;
using Cysharp.Threading.Tasks;
using OnePuz.Attributes;
using OnePuz.UI;
using UnityEngine;
using UnityEngine.UI;

public class UIPopupUpAdsRemoved : UIBasePanel
{

    private readonly int _showAnimation = Animator.StringToHash("Popup_TransitionIn");
    private readonly int _hideAnimation = Animator.StringToHash("Popup_TransitionOut");
    
    [Header("Popup Animation")] [SerializeField, PreAssigned()]
    private Animator _animator;
    [SerializeField, PreAssigned("Container/Popup/ButtonClose")]
    private Button _closeButton;
    
    protected override void OnAfterFocus()
    {
        base.OnAfterFocus();


        _closeButton.onClick.AddListener(OnCloseButtonClicked);
    }
    protected override void OnBeforeLostFocus()
    {
        base.OnBeforeLostFocus();


        _closeButton.onClick.RemoveListener(OnCloseButtonClicked);
    }
    private void OnCloseButtonClicked()
    {
        Close();
    }

    
    protected override async UniTask TransitionInAsync(CancellationToken cancellationToken)
    {
        await _animator.PlayManual(_showAnimation);
    }

    protected override async UniTask TransitionOutAsync(CancellationToken cancellationToken)
    {
        await _animator.PlayManual(_hideAnimation);
    }
}
