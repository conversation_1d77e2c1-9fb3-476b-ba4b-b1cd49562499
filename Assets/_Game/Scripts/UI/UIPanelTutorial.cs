using System.Threading;
using Cysharp.Threading.Tasks;
using OnePuz.Attributes;
using OnePuz.Extensions;
using PrimeTween;
using TMPro;
using UnityEngine;

namespace OnePuz.UI
{
    public class UIPanelTutorial : UIBasePanel
    {
        private readonly int _handAppearAnimation = Animator.StringToHash("Hand_Tut_Appear");
        private readonly int _handAppearIdleAnimation = Animator.StringToHash("Hand_Tut");
        private readonly int _handDisappearAnimation = Animator.StringToHash("Hand_Tut_Disappear");

        [Header("Tutorial")]
        [SerializeField, PreAssigned("Hand")]
        private RectTransform _handRectTransform;

        [SerializeField, PreAssigned("Hand")]
        private Animator _handAnimator;

        [SerializeField, PreAssigned("LabelInstruction")]
        private TextMeshProUGUI _tutorialInstructionText;

        private bool _isHandActive;

        public override void Init(string id)
        {
            base.Init(id);

            _tutorialInstructionText.gameObject.SetActive(false);
            _handAnimator.SetState(_handAppearAnimation);
            
            this.EventSubscribe<GameStateChangedEvent>(OnGameStateChanged);
        }

        private void OnGameStateChanged(GameStateChangedEvent e)
        {
            switch (e.currentState)
            {
                case GameState.WIN:
                case GameState.LOSE:
                case GameState.REPLAY:
                    HideTutorial();
                    break;
            }
        }

        protected override UniTask TransitionOutAsync(CancellationToken cancellationToken)
        {
            return UniTask.CompletedTask;
        }

        protected override UniTask TransitionInAsync(CancellationToken cancellationToken)
        {
            return UniTask.CompletedTask;
        }

        public void ShowTutorialMoreBolt(Vector3 handPosition)
        {
            ShowTutorial("", true, handPosition);
        }

        public void ShowTutorial(string instruction, bool showHand, Vector3 handPosition)
        {
            _tutorialInstructionText.gameObject.SetActive(true);
            _tutorialInstructionText.text = instruction;

            if (showHand)
            {
                if (!_isHandActive)
                {
                    Tween.StopAll(_handAnimator);
                    _handAnimator.PlayManual(_handAppearAnimation).OnComplete(() => { _handAnimator.PlayManual(_handAppearIdleAnimation, cycles: -1); });
                }
            }
            else
            {
                Tween.StopAll(_handAnimator);
                if (_isHandActive)
                    _handAnimator.PlayManual(_handDisappearAnimation);
                else
                    _handAnimator.SetState(_handDisappearAnimation, isReversed: true);
            }

            if (showHand)
            {
                PlaceUIAtWorldPosition(pCanvas, _handRectTransform, handPosition);
            }

            _isHandActive = showHand;
        }

        private void PlaceUIAtWorldPosition(Canvas canvas, RectTransform uiElement, Vector3 worldPosition)
        {
            // Convert the world position to a position on the screen
            Vector2 screenPosition = Camera.main.WorldToScreenPoint(worldPosition);

            // Convert the screen position to a position on the canvas
            RectTransformUtility.ScreenPointToLocalPointInRectangle(canvas.GetComponent<RectTransform>(), screenPosition, canvas.worldCamera, out var canvasPosition);

            // Set the position of the UI element
            uiElement.anchoredPosition = canvasPosition;
        }

        public void HideTutorial()
        {
            if (_isHandActive)
                _handAnimator.PlayManual(_handDisappearAnimation);

            _tutorialInstructionText.gameObject.SetActive(false);

            _isHandActive = false;
        }
    }
}