using System.Threading;
using _FeatureHub.Attributes.Core;
using Cysharp.Threading.Tasks;
using OnePuz.Extensions;
using OnePuz.Services;
using OnePuz.UI;
using PrimeTween;
using Spine.Unity;
using UnityEngine;

namespace _Game.Scripts.UI
{
    [ReferenceInBackground]
    public class UITakeABreak : UIBasePanel
    {
        [SerializeField, ReferenceValue("Container/IconAd (Spine)")]
        private SkeletonGraphic m_SkeletonAd;
        
        [SerializeField, ReferenceValue("Container/Label")]
        private RectTransform m_LblTakeABreak;
        
        protected override async UniTask TransitionInAsync(CancellationToken cancellationToken)
        {
            await Tween.Alpha(pCanvasGroup, 0, 1, 0.3f);
        }

        protected override async UniTask TransitionOutAsync(CancellationToken cancellationToken)
        {
            await Tween.Alpha(pCanvasGroup, 1, 0, 0.15f);
        }

        public override void Init(string id)
        {
            base.Init(id);
            this.EventSubscribe<EventABreak>(HandleTakeABreak);
        }

        private void HandleTakeABreak(EventABreak e)
        {
            Show();
            m_SkeletonAd.AnimationState.SetAnimation(0, "animation", false);
            Sequence.Create()
                .Group(Sequence.Create()
                    .Chain(Tween.UIAnchoredPositionY(m_LblTakeABreak, 64, 0.3f, Ease.InCubic))
                    .Chain(Tween.UIAnchoredPositionY(m_LblTakeABreak, -64, 0.5f, Ease.OutBounce)))
                .Group(Sequence.Create()
                    .Chain(Tween.Scale(m_LblTakeABreak ,Vector3.zero, 1.25f * Vector3.one, 0.3f, Ease.OutCirc))
                    .Chain(Tween.Scale(m_LblTakeABreak, Vector3.one, 0.5f, Ease.OutBounce)))
                .ChainCallback(e, eventABreak => eventABreak.Execute())
                .OnComplete(this, @this => @this.Close());
        }
    }
}