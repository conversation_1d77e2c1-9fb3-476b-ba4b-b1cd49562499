using System;
using System.Collections.Generic;
using OnePuz.Services.Extensions;
using OnePuz.TimeHandler;

namespace OnePuz.Services
{
    public interface ITimeEventService
    {
        void RegisterEvent(string id, TimeEventConfig config);
    }

    public class TimeEventConfig
    {
        public int delay;
        public int duration;
        public int interval;
    }

    public class TimeEventData
    {
        public string id;
        public TimeSpan interval;
        public DateTime startTime;
        public DateTime endTime;
    }
    
    public class TimeEventService : ITimeEventService, IServiceLoad
    {
        private Dictionary<string, TimeEventData> _events = new();

        private float _lastUnscaledTime;

        public void Load()
        {
            this.EventSubscribe<TimeUpdateEvent>(OnUpdate);
        }

        public void RegisterEvent(string id, TimeEventConfig config)
        {
            if (_events.ContainsKey(id))
                return;

            var data = new TimeEventData
            {
                id = id,
                startTime = DateTime.Now.AddSeconds(config.delay),
                endTime = DateTime.Now.AddSeconds(config.delay + config.duration)
            };
            _events.Add(id, data);
        }

        private void OnUpdate(TimeUpdateEvent e)
        {
            var unscaledTime = Core.Time.UnscaledTime;
            if (unscaledTime - _lastUnscaledTime < 1)
                return;

            _lastUnscaledTime = unscaledTime;
            
            CheckAllEvents();
        }

        private void CheckAllEvents()
        {
            foreach (var kvp in _events)
            {
                var data = kvp.Value;
                if (data.endTime < DateTime.Now)
                {
                    // Fire event
                    _events.Remove(kvp.Key);
                }
            }
        }
    }
}