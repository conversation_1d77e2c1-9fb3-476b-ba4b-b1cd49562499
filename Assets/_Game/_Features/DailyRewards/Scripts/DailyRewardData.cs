using System;
using Newtonsoft.Json;
using OnePuz.Data;

namespace OnePuz.DailyReward.Data
{
    public struct OnDailyRewardNotifyChanged
    {
    }

    [Serializable]
    public class DailyRewardData : ISaveData, IVersionedData
    {
        [JsonProperty]
        private int _currentDay = 0;

        [JsonProperty]
        private bool _isClaimed = false;

        [JsonProperty]
        private DateTime _claimedDate;

        [JsonIgnore]
        public int CurrentDay => _isClaimed ? _currentDay - 1 : _currentDay;
        [JsonIgnore]
        public bool IsClaimed => _isClaimed;
        [JsonIgnore]
        public DateTime ClaimedDate => _claimedDate;

        [JsonIgnore]
        public bool CanShowDailyRewardPopup => !_isClaimed && (DataShortcut.Level.Current >= 8 - 1 || DataShortcut.User.openedGameTime > 1);

        public override void SetupDefaultValues()
        {
        }

        public void Setup()
        {
            OLogger.LogNotice($"Setup DailyRewardData {_currentDay} {_isClaimed} {_claimedDate}");
            if (_claimedDate.Date != DateTime.Now.Date)
            {
                _isClaimed = false;
            }
        }

        public void Claim()
        {
            _isClaimed = true;

            _claimedDate = DateTime.Now;

            _currentDay++;

            if (_currentDay >= Core.Definition.DailyReward.data.Count)
            {
                _currentDay = 0;
            }

            Core.Event.Fire(new OnDailyRewardNotifyChanged());
        }

        public int Version { get; set; } = 0;

        public void Migrate()
        {
            if (Version < 1)
            {
            }
        }
    }
}