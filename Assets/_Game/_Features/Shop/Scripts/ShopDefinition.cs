using System.Collections.Generic;
using System.Linq;
using OnePuz.Data;
using OnePuz.Definition;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.Serialization;
using System.IO;
using System.Text;

namespace OnePuz.Shop.Definition
{
    [CreateAssetMenu(menuName = "OnePuz/Definitions/ShopDefinition")]
    public class ShopDefinition : ScriptableObject
    {
        public List<ShopItemData> bundles;
        public ShopItemData noAdsPack;
        public ShopItemData noAdsBundles;
        public ShopItemData freeCoinPack;
        public List<ShopItemData> goldPacks;
        public List<ShopItemData> gemPacks;
        public List<string> removeAdsProductIds = new List<string>();
        
        public Dictionary<string, ShopItemData> GetShopItemDictionary()
        {
            var shopItems = new Dictionary<string, ShopItemData>();

            if (bundles != null)
            {
                foreach (var item in bundles.Where(item => !string.IsNullOrEmpty(item.productId)))
                {
                    shopItems[item.productId] = item;
                }
            }

            if (noAdsPack != null && !string.IsNullOrEmpty(noAdsPack.productId))
            {
                shopItems[noAdsPack.productId] = noAdsPack;
            }

            if (noAdsBundles != null && !string.IsNullOrEmpty(noAdsBundles.productId))
            {
                shopItems[noAdsBundles.productId] = noAdsBundles;
            }

            // if (freeCoinPack != null && !string.IsNullOrEmpty(freeCoinPack.productId))
            // {
            shopItems[freeCoinPack.productId] = freeCoinPack;
            // }

            if (goldPacks == null) return shopItems;
            {
                foreach (var item in goldPacks.Where(item => !string.IsNullOrEmpty(item.productId)))
                {
                    shopItems[item.productId] = item;
                }
            }
            return shopItems;
        }

        [Button("Update Ids"), PropertyOrder(-1)]
        private void UpdateIds()
        {
            for (int i = 0; i < goldPacks.Count; i++)
            {
                goldPacks[i].id = $"gold_pack_{i}";
            }

            for (int i = 0; i < gemPacks.Count; i++)
            {
                gemPacks[i].id = $"gem_pack_{i}";
            }

#if UNITY_EDITOR
            UnityEditor.EditorUtility.SetDirty(this);
#endif
        }
        
        [Button("Update Remove Ads Product Ids"), PropertyOrder(-1)]
        private void UpdateRemoveAdsProductIds()
        {
            removeAdsProductIds.Clear();
            
            removeAdsProductIds.Add(noAdsPack.productId);
            removeAdsProductIds.Add(noAdsBundles.productId);

            foreach (var bundle in bundles)
            {
                if (bundle.rewards.Any(reward => reward.RewardType == RewardType.NO_ADS))
                {
                    removeAdsProductIds.Add(bundle.productId);
                }
            }

#if UNITY_EDITOR
            UnityEditor.EditorUtility.SetDirty(this);
#endif
        }

        private List<ProductInfo> GetAllProductInfo()
        {
            var productInfos = new List<ProductInfo>();

            if (bundles != null)
            {
                foreach (var item in bundles)
                {
                    AddProduct(item);
                }
            }

            AddProduct(noAdsPack);
            AddProduct(freeCoinPack);
            if (goldPacks != null)
            {
                foreach (var item in goldPacks)
                {
                    AddProduct(item);
                }
            }

            if (gemPacks == null) return productInfos;
            {
                foreach (var item in gemPacks)
                {
                    AddProduct(item);
                }
            }

            return productInfos;

            void AddProduct(ShopItemData item)
            {
                if (item == null || string.IsNullOrEmpty(item.productId))
                    return;
                var displayedPrice = item.price - 0.01;

                var rewardInfos = new List<RewardInfo>();
                if (item.rewards != null)
                {
                    rewardInfos.AddRange(item.rewards.Select(reward => new RewardInfo
                    {
                        RewardType = reward.RewardType.ToString(),
                        BoosterType = reward.BoosterType.ToString(),
                        Quantity = reward.Quantity
                    }));
                }

                var product = new ProductInfo
                {
                    Name = item.name,
                    ProductID = item.productId,
                    Price = displayedPrice,
                    Rewards = rewardInfos
                };
                productInfos.Add(product);
            }
        }

        [Button(ButtonSizes.Gigantic)]
        public void ExportProductsInfoToCsv()
        {
            var products = GetAllProductInfo();
            var csv = new StringBuilder();

            csv.AppendLine("Name,ProductID,Price,Rewards");

            foreach (var product in products)
            {
                var rewardsStr = "";
                if (product.Rewards is { Count: > 0 })
                {
                    var rewardsList = product.Rewards.Select(reward => reward.RewardType == "Booster"
                            ? $"{reward.RewardType} ({reward.BoosterType}: x{reward.Quantity})"
                            : $"{reward.RewardType} (x{reward.Quantity})")
                        .ToList();

                    rewardsStr = string.Join(";", rewardsList);
                }

                csv.AppendLine($"\"{product.Name}\",\"{product.ProductID}\",\"{product.Price}\",\"{rewardsStr}\"");
            }

            var folderPath = Application.dataPath;
            var filePath = Path.Combine(folderPath, "products.csv");

            File.WriteAllText(filePath, csv.ToString());
            Debug.Log($"CSV exported to: {filePath}");
        }
    }

    [System.Serializable]
    public class ShopItemData
    {
        [HorizontalGroup("General")] [VerticalGroup("General/Left")]
        public string id;

        [VerticalGroup("General/Left")] public string name;
        [VerticalGroup("General/Left")] public string description;

        [VerticalGroup("General/Left")] [PreviewField(100)]
        public Sprite icon;

        [VerticalGroup("General/Left")] public string tagName;

        [VerticalGroup("General/Right")] public CurrencyType priceType;
        [VerticalGroup("General/Right")] public int price;

        [VerticalGroup("General/Right")] [ShowIf("priceType", CurrencyType.MONEY)]
        public string productId;

        [VerticalGroup("General/Right")] public List<RewardData> rewards;
    }

    public class ProductInfo
    {
        public string Name;
        public string ProductID;
        public double Price;
        public List<RewardInfo> Rewards;
    }

    public class RewardInfo
    {
        public string RewardType;
        public string BoosterType;
        public int Quantity;
    }
}