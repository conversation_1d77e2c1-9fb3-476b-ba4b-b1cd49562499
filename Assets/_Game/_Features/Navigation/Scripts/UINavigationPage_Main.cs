using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using OnePuz.Definition;
using UnityEngine;
using UnityEngine.UI;

namespace OnePuz.UI.Navigation
{
    public class UINavigationPage_Main : UINavigationPage, IContainRewardTarget
    {
        [Header("Buttons")]
        [SerializeField]
        private Button _settingButton;
        [SerializeField]
        private Button _battleButton;

        [Header("Currencies")]
        [SerializeField]
        private UICurrency _coinCurrency;
        [SerializeField]
        private UICurrency _gemCurrency;

        IRewardTarget IContainRewardTarget.GetRewardTarget<T>(T type)
        {
            switch (type)
            {
                case CurrencyType currencyType:
                    return currencyType switch
                    {
                        CurrencyType.COIN => _coinCurrency,
                        CurrencyType.GEM => _gemCurrency,
                        _ => null
                    };
                case BoosterType boosterType:
                    return _battleButton.GetComponent<IRewardTarget>();
                default:
                    return null;
            }
        }
        
        #region Callbacks
        internal override void OnBeforeFocus()
        {
            base.OnBeforeFocus();

            _coinCurrency.Init();
            _gemCurrency.Init();
        }

        internal override void OnAfterFocus()
        {
            base.OnAfterFocus();
            
            _battleButton.onClick.AddListener(OnBattleButtonClicked);

            _settingButton.onClick.AddListener(OnSettingButtonClicked);
        }

        internal override void OnBeforeLostFocus()
        {
            base.OnBeforeLostFocus();

            _battleButton.onClick.RemoveListener(OnBattleButtonClicked);

            _settingButton.onClick.RemoveListener(OnSettingButtonClicked);
        }

        internal override void OnAfterLostFocus()
        {
            base.OnAfterLostFocus();
        }

        private void OnBattleButtonClicked()
        {
            Core.Game();
        }

        private void OnSettingButtonClicked()
        {
            UIShortcut.ShowPopup(UIKeys.Panel.SETTINGS);
        }
        #endregion
    }
}