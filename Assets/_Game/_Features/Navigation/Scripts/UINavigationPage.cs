using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace OnePuz.UI.Navigation
{
    public abstract class UINavigationPage : MonoBehaviour, IPanel, IRelativePanel
    {
        public Canvas pCanvas { get; }
        public CanvasGroup pCanvasGroup { get; }
        public string pId { get; private set; }

        public bool pIsOpen => pState == PanelState.OPENED || pState == PanelState.OPENING;
        
        public bool pIsPopup => false;

        public PanelState pState { get; private set; } = PanelState.CLOSED;

        public PanelArgs pArgs { get; set; }
        
        public IPanel pManagerPanel { get; private set; } = null;

        private GameObject _gameObject;
        public GameObject pGameObject => _gameObject ??= gameObject;

        protected UINavigation _manager;
        
        protected CancellationToken _cancellationToken;

        public virtual void Init(string id)
        {
            pId = id;

            pState = PanelState.CLOSED;
        }
        
        public void RegisterCancellation(CancellationToken cancellationToken)
        {
            _cancellationToken = cancellationToken;
        }

        public void AssignManager(UINavigation manager, IPanel mainPanel)
        {
            _manager = manager;
            pManagerPanel = mainPanel;
        }

        public UniTask ShowAsync(PanelArgs args)
        {
            pArgs = args;

            _manager.Navigate(pId);
            return UniTask.CompletedTask;
        }

        public UniTask CloseAsync(bool instant = false)
        {
            return UniTask.CompletedTask;
        }

        public void Show(PanelArgs args = null)
        {
            ShowAsync(args).Forget();
        }

        public void Close()
        {
            CloseAsync().Forget();
        }

        internal virtual void OnBeforeFocus() 
        { 
            pState = PanelState.OPENING;
        }

        internal virtual void OnAfterFocus() 
        { 
            pState = PanelState.OPENED;
        }

        internal virtual void OnBeforeLostFocus() 
        { 
            pState = PanelState.CLOSING;
        }

        internal virtual void OnAfterLostFocus() 
        { 
            pState = PanelState.CLOSED;
        }
    }

}