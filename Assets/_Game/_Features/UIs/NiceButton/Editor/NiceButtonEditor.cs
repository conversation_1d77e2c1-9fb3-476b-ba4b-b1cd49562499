using UnityEditor;
using UnityEditor.UI;
using UnityEngine;
using UnityEngine.UI;

namespace OnePuz.UI.Editor
{
    [CustomEditor(typeof(NiceButton), true)]
    [CanEditMultipleObjects]
    public class NiceButtonEditor : SelectableEditor
    {
        private GUIStyle m_TitleStyle;
        private SerializedProperty m_OnClick;
        private SerializedProperty m_KeyProperty;
        private SerializedProperty m_ListenableProperty;
        private SerializedProperty m_Type;
        private NiceButton m_Script;

        private SerializedProperty m_EnableVibration;
        private SerializedProperty m_SoundKey;

        protected override void OnEnable()
        {
            base.OnEnable();
            m_Script = target as NiceButton;
            m_TitleStyle = new GUIStyle { fontSize = 16, fontStyle = FontStyle.Bold, richText = true, alignment = TextAnchor.MiddleCenter };
            m_ListenableProperty = serializedObject.FindProperty("listenable");
            m_Type = serializedObject.FindProperty("type");
            m_OnClick = serializedObject.FindProperty("m_OnClick");
            m_SoundKey = serializedObject.FindProperty("_soundKey");
            m_EnableVibration = serializedObject.FindProperty("enableVibration");
        }

        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();
            serializedObject.Update();
            EditorGUILayout.PropertyField(m_OnClick);
            EditorGUILayout.Separator();
            GUILayout.Label("<b><color=grey>___________________ Nice Features ____________________</color></b>", m_TitleStyle);
            EditorGUILayout.Separator();

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.PropertyField(m_Type);
            if (m_Script.type == NiceButton.ClickType.Animated)
            {
                EditorGUI.BeginDisabledGroup(true);
                EditorGUILayout.PropertyField(m_ListenableProperty);
                EditorGUI.EndDisabledGroup();
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.PropertyField(m_SoundKey);
            EditorGUILayout.PropertyField(m_EnableVibration);

            EditorGUILayout.Separator();
            if (GUILayout.Button("Resize with closest Image"))
            {
                foreach (var obj in Selection.gameObjects)
                {
                    var niceButton = obj.GetComponent<NiceButton>();
                    if (niceButton != null)
                        niceButton.ResizeWithClosestImage();
                }
            }

            serializedObject.ApplyModifiedProperties();
            if (GUI.changed)
            {
                m_Script.RefreshAnimator();
            }
        }

#if UNITY_EDITOR
        [MenuItem("GameObject/UI/OnePuz/NiceButton")]
        public static void CreateInstance()
        {
            var obj = new GameObject("Button");
            obj.transform.SetParent(Selection.activeTransform);
            var rect = obj.AddComponent<RectTransform>();
            rect.localScale = Vector3.one;
            rect.anchoredPosition3D = Vector3.zero;
            obj.AddComponent<Image>();
            var niceButton = obj.AddComponent<NiceButton>();
            niceButton.transition = Selectable.Transition.Animation;
            niceButton.RefreshAnimator();
            niceButton.Rebuild();
            Selection.activeObject = obj;
        }
#endif
    }
}