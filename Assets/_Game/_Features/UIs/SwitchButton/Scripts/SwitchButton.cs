using OnePuz.Attributes;
using OnePuz.Extensions;
using UnityEngine;
using UnityEngine.UI;

namespace OnePuz.UI
{
    [RequireComponent(typeof(Animator), typeof(Toggle))]
    public class SwitchButton : MonoBehaviour
    {
        [SerializeField, PreAssigned()]
        private Animator _animator;
        
        [SerializeField, PreAssigned()]
        private Toggle _toggle;

        public SimpleBoolCallback OnValueChanged;

        private void OnDestroy()
        {
            _toggle?.onValueChanged.RemoveListener(Ins_OnValueChanged);
        }

        public void Init(bool isOn)
        {
            _animator.SetState(isOn ? Animator.StringToHash("SwitchButton_Off") : Animator.StringToHash("SwitchButton_On"));
            
            _toggle.isOn = isOn;
            _toggle.onValueChanged.AddListener(Ins_OnValueChanged);
        }

        private void Ins_OnValueChanged(bool isOn)
        {
            OnValueChanged?.Invoke(isOn);
            
            _animator.PlayManual(isOn ? Animator.StringToHash("SwitchButton_On") : Animator.StringToHash("SwitchButton_Off"));
        }
    }
}