using OnePuz.Attributes;
using UnityEngine;
using UnityEngine.UI;

namespace OnePuz.UI
{
    public class ToggleButton : MonoBehaviour
    {
        [SerializeField, PreAssigned()]
        private Toggle _toggle;
        
        [SerializeField, PreAssigned("Enable")]
        private GameObject _enable;
        
        [SerializeField, PreAssigned("Disable")]
        private GameObject _disable;
        
        public SimpleBoolCallback onValueChanged;
        
        private void Awake() => _toggle.onValueChanged.AddListener(HandleOnValueChanged);

        private void OnDestroy() => _toggle.onValueChanged.RemoveListener(HandleOnValueChanged);

        public void SetValue(bool isOn) => _toggle.isOn = isOn;

        private void HandleOnValueChanged(bool isOn)
        {
            if (_disable) _disable.SetActive(!isOn);
            if (_enable) _enable.SetActive(isOn);
            onValueChanged?.Invoke(isOn);
        }
    }
}