using Newtonsoft.Json;
using OnePuz.Services;
using UnityEngine;
using UnityEngine.Serialization;

namespace OnePuz.Data
{
    [System.Serializable]
    public class LevelChestData : ISaveData, IVersionedData
    {
        [JsonProperty]
        private int _playedLevels = 0;
        
        [JsonIgnore]
        public int PlayedLevels => _playedLevels;
        
        public override void SetupDefaultValues()
        {
            _playedLevels = 0;
        }
        
        public void CompleteLevel()
        {
            _playedLevels++;
        }
        
        public void ConfirmOpenOrSkipChest()
        {
            _playedLevels = 0;
        }

        public int Version { get; set; } = 0;
        
        public void Migrate()
        {
            if (Version < 1)
            {
            }
        }
    }
}