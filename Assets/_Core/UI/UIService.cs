using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using OnePuz.Extensions;
using Cysharp.Threading.Tasks;
using OnePuz.Data;
using OnePuz.Services;
using OnePuz.Services.Extensions;
using PrimeTween;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.EventSystems;
using UnityEngine.Rendering.Universal;
using Object = UnityEngine.Object;

namespace OnePuz.UI
{
    public enum ShowMode
    {
        PERSISTENT, // Open other panels won't close this panel
        NON_PERSISTENT, // Close other non-persistent panels when show this panel
        HISTORY, // Show panel then add to history, when close, show last panel
        APPEND // Won't close other panels, add this panel to queue, then wait for the last panel to close before showing this panel
    }

    public struct OnShownPanelEvent
    {
        public string panelId;
        public ShowMode showMode;
        public int panelSortingOrder;
    }

    public struct OnClosedPanelEvent
    {
        public string panelId;
        public ShowMode showMode;
    }

    public struct OnClosedLastPopupEvent
    {
        public string panelId;
    }

    public class UIService : IServiceLoad, IServiceUnload
    {
        private Transform _container;
        private Camera _camera;
        private CanvasGroup _shadowCanvasGroup;
        public EventSystem pEventSystem { get; private set; }

        private readonly Dictionary<string, IPanel> _loadedPanelDictionary = new();
        private readonly Dictionary<string, ResourceAsset> _loadedPanelResourceAssets = new();

        private UIPrefabDefinitions _uiPrefabDefinitions;
        private readonly Stack<string> _historyPopups = new();
        private Queue<string> _queuePopups = new();

        private bool _alreadyLoadedAdditional;

        public void Load()
        {
            _uiPrefabDefinitions = Core.Get<DefinitionService>().UIPrefabs;

            LoadContainer();

            this.EventSubscribe<UnitySceneLoadedEvent>(OnSceneLoaded);
        }

        private void OnSceneLoaded(UnitySceneLoadedEvent e)
        {
#if UNITY_EDITOR
            if (e.sceneIndex != 0)
                LoadAdditional();
            
            if (Camera.main != null)
            {
                var cameraData = _camera.GetUniversalAdditionalCameraData();
                if (cameraData && cameraData.renderType == CameraRenderType.Base)
                {
                    cameraData.renderType = CameraRenderType.Overlay;
                }
            }
#endif
        }

        private void LoadContainer()
        {
            _container = new GameObject("[UIContainer]").transform;

            var cameraPrefab = Resources.Load<GameObject>("UI/UICamera");
            _camera = GameObject.Instantiate(cameraPrefab).GetComponent<Camera>();
            _camera.transform.SetParent(_container);
            _camera.transform.localPosition = cameraPrefab.transform.localPosition;
            _camera.name = cameraPrefab.name;
            
            var cameraData = _camera.GetUniversalAdditionalCameraData();
            if (cameraData)
            {
                cameraData.renderType = CameraRenderType.Base;
                _camera.clearFlags = CameraClearFlags.SolidColor;
                _camera.backgroundColor = Color.clear;
            }

            var eventSystemPrefab = Resources.Load<GameObject>("UI/EventSystem");
            var eventSystem = GameObject.Instantiate(eventSystemPrefab, _container);
            eventSystem.name = eventSystemPrefab.name;
            pEventSystem = eventSystem.GetComponent<EventSystem>();

            var audioListenerInScene = Object.FindAnyObjectByType<AudioListener>();
            if (!audioListenerInScene)
            {
                var audioListenerPrefab = Resources.Load<GameObject>("UI/AudioListener");
                var audioListener = GameObject.Instantiate(audioListenerPrefab, _container);
                audioListener.name = audioListenerPrefab.name;
            }

            GameObject.DontDestroyOnLoad(_container.gameObject);
        }

        public void LoadAdditional()
        {
            if (_alreadyLoadedAdditional)
                return;

            var shadowCanvasPrefab = Resources.Load<GameObject>("UI/Panels/Shadow");
            var shadowCanvasTransform = GameObject.Instantiate(shadowCanvasPrefab, _container).transform;
            var shadowCanvas = shadowCanvasTransform.GetComponent<Canvas>();
            shadowCanvas.renderMode = RenderMode.ScreenSpaceCamera;
            shadowCanvas.sortingOrder = (int)PanelLayer.POPUP - 1;
            shadowCanvas.worldCamera = _camera;

            _shadowCanvasGroup = shadowCanvasTransform.FindDeep<CanvasGroup>("Container");
            _shadowCanvasGroup.alpha = 0f;
            _shadowCanvasGroup.interactable = false;
            _shadowCanvasGroup.blocksRaycasts = false;

            _alreadyLoadedAdditional = true;

            LoadPanelsAsync(_uiPrefabDefinitions.Global, this.GetDestroyCancellationToken()).Forget();
        }

        private List<UIPrefabDefinitions.Datum> GetPanelDataBySceneName(string sceneName)
        {
            return sceneName switch
            {
                "Bootstrap" => _uiPrefabDefinitions.Loading,
                "Home" => _uiPrefabDefinitions.Home,
                "Game" => _uiPrefabDefinitions.Game,
                _ => null
            };
        }

        public async UniTask LoadPanelsAsync(string sceneName, CancellationToken cancellationToken)
        {
            _historyPopups.Clear();
            _queuePopups.Clear();

            var panelData = GetPanelDataBySceneName(sceneName);
            await LoadPanelsAsync(panelData, cancellationToken);
        }

        private async UniTask LoadPanelsAsync(List<UIPrefabDefinitions.Datum> panelData, CancellationToken cancellationToken)
        {
            if (panelData == null)
                return;

            foreach (var datum in panelData)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                if (_loadedPanelDictionary.ContainsKey(datum.Id)) continue;

                _loadedPanelResourceAssets[datum.Id] = datum.PrefabReference;

                var prefab = await datum.PrefabReference.LoadAsync<GameObject>();
                var panel = GameObject.Instantiate(prefab, _container).GetComponent<UIBasePanel>();
                panel.transform.localPosition = Vector3.zero;
                panel.transform.localScale = Vector3.one;
                panel.transform.localRotation = Quaternion.identity;
                panel.name = $"{(datum.Layer == PanelLayer.POPUP ? "Popup" : "Panel")} [{prefab.name}]";

                panel.Init(datum.Id);

                panel.RegisterCancellation(cancellationToken);

                if (datum.Layer != PanelLayer.OVERLAY)
                {
                    panel.pCanvas.renderMode = RenderMode.ScreenSpaceCamera;
                    panel.pCanvas.worldCamera = _camera;
                }
                else
                {
                    panel.pCanvas.renderMode = RenderMode.ScreenSpaceOverlay;
                    panel.pCanvas.worldCamera = null;
                }

                panel.pCanvas.sortingOrder = datum.LayerOrder;

                _loadedPanelDictionary.Add(datum.Id, panel);
                
                if (datum.AlwaysActive)
                {
                    panel.Show();
                }

                await UniTask.Yield(cancellationToken: cancellationToken);
            }

            SortPanelsInHierarchy();
        }

        private void SortPanelsInHierarchy()
        {
#if UNITY_EDITOR
            var basePanels = new List<UIBasePanel>();
            foreach (var panel in _loadedPanelDictionary.Values)
            {
                //TODO: Check if null panel loading.
                if (panel is null)
                    continue;
                if (panel is IRelativePanel relativePanel)
                {
                    basePanels.Add((UIBasePanel)relativePanel.pManagerPanel);
                }
                else
                {
                    basePanels.Add((UIBasePanel)panel);
                }
            }

            // Sort by sortingOrder
            var sortedPanels = basePanels.OrderBy(p => p.pCanvas.sortingOrder).ToList();
            foreach (var panel in sortedPanels)
            {
                panel.transform.SetAsLastSibling();
            }
#endif
        }

        public async UniTask UnloadPanelsAsync(string sceneName, CancellationToken cancellationToken)
        {
            var panelData = GetPanelDataBySceneName(sceneName);
            await UnloadPanelsAsync(panelData, cancellationToken);

            UnloadAttachedScenePanels(sceneName);

            _historyPopups.Clear();
            _queuePopups.Clear();
        }

        private async UniTask UnloadPanelsAsync(List<UIPrefabDefinitions.Datum> panelData, CancellationToken cancellationToken)
        {
            if (panelData == null)
                return;

            foreach (var data in panelData)
            {
                if (!_loadedPanelDictionary.TryGetValue(data.Id, out var panel)) continue;
                if (panel == null)
                {
                    removePanel(data.Id);
                    continue;
                }

                if (panel.pIsOpen)
                {
                    panel.CloseAsync(instant: true).Forget();
                }

                if (panel is IRelativePanel relativePanel)
                {
                    GameObject.Destroy(relativePanel.pManagerPanel.pGameObject);
                }
                else
                {
                    GameObject.Destroy(panel.pGameObject);
                }

                removePanel(data.Id);
                await UniTask.Yield(cancellationToken: cancellationToken);
            }

            return;

            void removePanel(string id)
            {
                _loadedPanelDictionary.Remove(id);
                if (!_loadedPanelResourceAssets.TryGetValue(id, out var assetRef)) return;
                assetRef.Unload();
                _loadedPanelResourceAssets.Remove(id);
            }
        }

        private void UnloadAttachedScenePanels(string sceneName)
        {
            var keysToRemove = new List<string>();
            foreach (var pair in _loadedPanelDictionary)
            {
                if (pair.Value is IAttachedScenePanel attachScenePanel && attachScenePanel.pAttachedSceneId == sceneName)
                {
                    OLogger.Log($"Unload: {pair.Value.pGameObject.name}");
                    keysToRemove.Add(pair.Key);
                }
            }

            foreach (var key in keysToRemove)
            {
                if (!_loadedPanelDictionary.TryGetValue(key, out var panel)) continue;

                if (panel.pIsOpen)
                {
                    panel.CloseAsync(instant: true).Forget();
                }

                if (panel is IRelativePanel relativePanel)
                {
                    Object.Destroy(relativePanel.pManagerPanel.pGameObject);
                }
                else
                {
                    Object.Destroy(panel.pGameObject);
                }

                try
                {
                    _loadedPanelDictionary.Remove(key);
                    if (_loadedPanelResourceAssets.TryGetValue(key, out var assetReference))
                    {
                        assetReference.Unload();
                        _loadedPanelResourceAssets.Remove(key);
                    }
                }
                catch (Exception e)
                {
                    OLogger.LogError($"UnloadAttachedScenePanels {e.Message} {e.StackTrace}");
                }
            }
        }

        private async UniTask<IPanel> ShowAsync(string id, PanelArgs args = null, bool isPriority = false)
        {
            if (!_loadedPanelDictionary.TryGetValue(id, out var panel))
            {
                args?.Release();
                return null;
            }

            args ??= PanelArgs.Default;

            switch (args.mode)
            {
                case ShowMode.PERSISTENT:
                    // Code for persistent mode
                    break;
                case ShowMode.NON_PERSISTENT:
                    await CloseAllPopupAsync(panel);

                    _historyPopups.Clear();
                    _queuePopups.Clear();
                    break;
                case ShowMode.HISTORY:
                    if (_historyPopups.Count > 0)
                    {
                        var lastPanelId = _historyPopups.Peek();
                        var lastPanel = _loadedPanelDictionary[lastPanelId];
                        await lastPanel.CloseAsync();
                    }
                    else
                    {
                        var currentActivePopupId = await CloseAllPopupAsync(panel);
                        if (!string.IsNullOrEmpty(currentActivePopupId))
                        {
                            _historyPopups.Push(currentActivePopupId);
                        }
                    }

                    _historyPopups.Push(id);
                    // _queuePopups.Clear();
                    break;
                case ShowMode.APPEND:
                    _historyPopups.Clear();
                    break;
                default:
                    break;
            }

            if (args.mode == ShowMode.APPEND && IsAnyPopupOpen())
            {
                panel.pArgs = args;
                
                // Add to priority queue or regular queue based on isPriority parameter
                if (isPriority)
                {
                    // For priority, we need to add to the front of the queue
                    var tempQueue = new Queue<string>();
                    tempQueue.Enqueue(id);
                    
                    foreach (var queuedId in _queuePopups) 
                        tempQueue.Enqueue(queuedId);
                    
                    _queuePopups = tempQueue;
                }
                else
                {
                    // Regular queue behavior - add to the end
                    _queuePopups.Enqueue(id);
                }
                
                return null;
            }

            if (args.enableShadow) ShowShadowAsync(args.shadowAlpha).Forget();

            Core.Event.Fire(new OnShownPanelEvent { panelId = id, showMode = args.mode, panelSortingOrder = panel.pCanvas.sortingOrder });

            await panel.ShowAsync(args);

            return panel;

            async UniTask<string> CloseAllPopupAsync(IPanel checkPanel)
            {
                var currentActivePopupId = "";
                var panels = _loadedPanelDictionary.Values;
                foreach (var panel in panels.Where(panel => panel.pIsOpen && panel.pIsPopup))
                {
                    currentActivePopupId = panel.pId;
                    break;
                }

                await UniTask.WhenAll(panels
                    .Where(p => p.pIsOpen && p.pIsPopup && (checkPanel == null || p != checkPanel))
                    .Select(p => p.CloseAsync()));
                return currentActivePopupId;
            }
        }

        public async UniTask CloseAsync(string id)
        {
            if (_loadedPanelDictionary.TryGetValue(id, out var panel))
            {
                IPanel nextPanel = null;
                PanelArgs nextPanelArgs = null;

                if (_historyPopups.Count > 0 && _historyPopups.Peek() == id && panel.pIsPopup)
                {
                    _historyPopups.Pop();
                }

                if (_historyPopups.Count > 0 && panel.pIsPopup)
                {
                    var lastPanelId = _historyPopups.Peek();
                    nextPanel = _loadedPanelDictionary[lastPanelId];
                    nextPanelArgs = nextPanel.pArgs;
                }
                else if (_queuePopups.Count > 0 && panel.pIsPopup)
                {
                    var nextPanelId = _queuePopups.Dequeue();
                    nextPanel = _loadedPanelDictionary[nextPanelId];
                    nextPanelArgs = nextPanel.pArgs;
                }
                else
                {
                    _historyPopups.Clear();
                    _queuePopups.Clear();
                }

                var closeAsync = panel.CloseAsync();
                
                Core.Event.Fire(new OnClosedPanelEvent() { panelId = id, showMode = panel.pArgs?.mode ?? ShowMode.PERSISTENT });
                
                if (panel.pIsPopup && nextPanel == null && !IsAnyPopupOpen())
                {
                    Core.Event.Fire(new OnClosedLastPopupEvent() { panelId = id });
                }

                if (nextPanelArgs is not { enableShadow: true } && !IsAnyPopupOpen())
                    HideShadowAsync().Forget();

                await closeAsync;

                if (nextPanel != null && nextPanelArgs != null)
                    await nextPanel.ShowAsync(nextPanelArgs);
            }
        }

        public bool IsAnyPopupOpen()
        {
            return _loadedPanelDictionary.Values.Any(p => p.pIsOpen && p.pIsPopup);
        }

        public void CloseAllPanels(Action onCompleted = null)
        {
            foreach (var panel in _loadedPanelDictionary.Values)
            {
                panel.CloseAsync(instant: true).Forget();
            }

            onCompleted?.Invoke();
        }

        public T Get<T>() where T : IPanel
        {
            foreach (var panel in _loadedPanelDictionary.Values)
            {
                if (panel is T)
                {
                    return (T)panel;
                }
            }

            return default;
        }

        public T Get<T>(string id) where T : IPanel
        {
            if (!_loadedPanelDictionary.TryGetValue(id, out var panel)) return default;
            if (panel is T value)
            {
                return value;
            }

            return default;
        }

        public List<IPanel> GetOpenPanels()
        {
            return _loadedPanelDictionary.Values.Where(p => p.pIsOpen).ToList();
        }

        public void RegisterRelativePanel(string id, IPanel panel)
        {
            if (_loadedPanelDictionary.ContainsKey(id))
            {
                _loadedPanelDictionary[id] = panel;
            }
            else
            {
                _loadedPanelDictionary.TryAdd(id, panel);
            }
        }

        public void RegisterLazyPanel(string id, UIBasePanel panel, PanelLayer layer = PanelLayer.UI, int layerOrder = 1)
        {
            if (_loadedPanelDictionary.ContainsKey(id)) return;

            panel.transform.localPosition = Vector3.zero;
            panel.transform.localScale = Vector3.one;
            panel.transform.localRotation = Quaternion.identity;
            panel.name = $"{(layer == PanelLayer.POPUP ? "Popup" : "Panel")} [{panel.gameObject.name}]";

            panel.Init(id);

            panel.RegisterCancellation(this.GetDestroyCancellationToken());

            if (layer != PanelLayer.OVERLAY)
            {
                panel.pCanvas.renderMode = RenderMode.ScreenSpaceCamera;
                panel.pCanvas.worldCamera = _camera;
            }
            else
            {
                panel.pCanvas.renderMode = RenderMode.ScreenSpaceOverlay;
                panel.pCanvas.worldCamera = null;
            }

            panel.pCanvas.sortingOrder = layerOrder;

            _loadedPanelDictionary.Add(id, panel);
        }

        private async UniTaskVoid ShowShadowAsync(float alpha = 0.8f)
        {
            if (_shadowCanvasGroup.alpha > 0f) return;
            _shadowCanvasGroup.interactable = true;
            _shadowCanvasGroup.blocksRaycasts = true;
            await Tween.Alpha(_shadowCanvasGroup, alpha, 0.3f).ToYieldInstruction().ToUniTask(cancellationToken: this.GetDestroyCancellationToken());
        }

        private async UniTaskVoid HideShadowAsync()
        {
            if (_shadowCanvasGroup.alpha.Approx(0)) return;
            await Tween.Alpha(_shadowCanvasGroup, 0f, 0.15f).ToYieldInstruction().ToUniTask(cancellationToken: this.GetDestroyCancellationToken());

            _shadowCanvasGroup.interactable = false;
            _shadowCanvasGroup.blocksRaycasts = false;
        }

        public void ClearHistory()
        {
            _historyPopups.Clear();
        }

        public void ClearQueuePopup()
        {
            _historyPopups.Clear();
        }

        public void ChangeAllOpenedPanelsCanvasGroupAlpha(float alpha)
        {
            foreach (var panel in _loadedPanelDictionary.Values)
            {
                if (!panel.pIsOpen) continue;
                panel.pCanvasGroup.alpha = alpha;
            }
        }
        
        public Camera GetUICamera()
        {
            return _camera;
        }

        #region Shorthand

        internal void Show(string id, PanelArgs args, bool isPriority = false)
        {
            ShowAsync(id, args, isPriority).Forget();
        }

        internal void ShowWithPriority(string id, PanelArgs args)
        {
            ShowAsync(id, args, true).Forget();
        }

        private void ShowPanelWithSavedArgs(string id)
        {
            var panel = Get<IPanel>(id);
            if (panel != null)
            {
                ShowPanel(id, panel.pArgs);
            }
        }

        public void ShowPanel(string id, PanelArgs args = null)
        {
            args ??= PanelArgs.Default;
            args.mode = ShowMode.PERSISTENT;

            ShowAsync(id, args).Forget();
        }

        public async UniTask<IPanel> ShowPanelAsync(string id, PanelArgs args = null)
        {
            args ??= PanelArgs.Default;

            return await ShowAsync(id, args);
        }

        public void ShowPopup(string id, PanelArgs args = null)
        {
            args ??= PanelArgs.Popup;

            ShowAsync(id, args).Forget();
        }

        public void EnqueuePopup(string id, PanelArgs args = null)
        {
            args ??= PanelArgs.Popup;
            args.mode = ShowMode.APPEND;

            ShowAsync(id, args).Forget();
        }

        public void ShowPopupWithHistory(string id, PanelArgs args = null)
        {
            args ??= PanelArgs.Popup;
            args.mode = ShowMode.HISTORY;

            ShowAsync(id, args).Forget();
        }

        public async UniTask<IPanel> ShowPopupAsync(string id, PanelArgs args = null)
        {
            args ??= PanelArgs.Popup;

            return await ShowAsync(id, args);
        }

        public void Close(string id)
        {
            CloseAsync(id).Forget();
        }

        public void ClosePopup(string id)
        {
            ClosePopupAsync(id).Forget();
        }

        public void ClosePanel(string id)
        {
            ClosePanelAsync(id).Forget();
        }

        public async UniTask ClosePopupAsync(string id)
        {
            await CloseAsync(id);
        }

        public async UniTask ClosePanelAsync(string id)
        {
            await CloseAsync(id);
        }

        #endregion

        public void Unload()
        {
            Debug.Log("UIManagerService Unloaded");

            _loadedPanelDictionary.Clear();
            foreach (var assetRef in _loadedPanelResourceAssets.Values)
            {
                assetRef?.Unload();
            }

            _loadedPanelResourceAssets.Clear();

            this.EventUnsubscribe<UnitySceneLoadedEvent>(OnSceneLoaded);
        }
    }
}