using System;
using UnityEditor;
using UnityEngine;
using UnityEngine.EventSystems;

namespace _FeatureHub.UI.Runtime
{
    public abstract class BaseButton : UnityEngine.UI.Button
    {
        public enum ClickType : byte
        {
            None = 0,
            Animated
        }

        public ClickType m_Type;
        
        public bool m_Listenable;

        // private PointerEventData m_EventData;
        
        private object m_TargetObject;
        private object m_CallbackObject;
        private event Action<BaseButton> m_OnClickEvent;
#if UNITY_EDITOR
        public void Refresh()
        {
            if (transition != Transition.Animation)
                return;

            foreach (var entry in Selection.gameObjects)
            {
                var component = entry.GetComponent<Animator>();
                if (component is null)
                    component = entry.AddComponent<Animator>();
                else if (component.runtimeAnimatorController is not null)
                    return;

                component.runtimeAnimatorController = Resources.Load<RuntimeAnimatorController>("Button (Legacy)");
            }
        }
#endif

        public void SetClickListener<T>(T target, Action<T> callback) where T : class
        {
            m_OnClickEvent = null;
            m_TargetObject = target;
            m_CallbackObject = callback;
            m_OnClickEvent += @this => (@this.m_CallbackObject as Action<T>)?.Invoke(@this.m_TargetObject as T);
        }
        
        public override void OnPointerClick(PointerEventData eventData)
        {
            if (eventData.button != PointerEventData.InputButton.Left)
                return;
            
            if (!IsActive() || !IsInteractable())
                return;
            
            switch (m_Type)
            {
                case ClickType.Animated:
                    m_Listenable = true;
                    // m_EventData = eventData;
                    break;
                default:
                    UISystemProfilerApi.AddMarker("Button.onClick", this);
                    m_OnClickEvent?.Invoke(this);
                    onClick.Invoke();
                    OnClickResolved();
                    break;
            }
            OnAnyClick();
        }

        protected abstract void OnAnyClick();
        protected abstract void OnClickResolved();

        public void ExecuteListener()
        {
            if (!m_Listenable)
                return;

            m_Listenable = false;
            UISystemProfilerApi.AddMarker("Button.onClick", this);
            m_OnClickEvent?.Invoke(this);
            onClick.Invoke();
            OnClickResolved();
        }
    }
}