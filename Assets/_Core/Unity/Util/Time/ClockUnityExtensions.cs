// SPDX-License-Identifier: MIT
// Copyright (c) 2016-2021 <PERSON> (@JuDelCo)

#if UNITY_2019_3_OR_NEWER

using OnePuz.Handlers;
using UnityEngine;

namespace OnePuz.TimeHandler
{
	public partial class Clock<T> : IClock where T : ITimeDeltaEvent
	{
		public Clock(Behaviour behaviour, bool alwaysActive = false) : this()
		{
			var linkHandler = new BehaviourLinkHandler(behaviour, alwaysActive);
			this.updateCondition = () => linkHandler.IsActive;
		}

		public Clock(float elapsedSeconds, Behaviour behaviour, bool alwaysActive = false) : this(elapsedSeconds)
		{
			var linkHandler = new BehaviourLinkHandler(behaviour, alwaysActive);
			this.updateCondition = () => linkHandler.IsActive;
		}
	}
}

#endif
