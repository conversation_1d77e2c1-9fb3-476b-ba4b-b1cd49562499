using UnityEditor;
using UnityEngine;
using UnityEngine.UI;

namespace OnePuz.UI
{
    public static class UIContextExtender
    {
        #if UNITY_EDITOR
        [MenuItem("CONTEXT/Image/Resize Parent")]
        private static void ConvertToNiceButton(MenuCommand command)
        {
            var image = command.context as Image;
            if (image == null)
                return;
            
            var rectTransform = image.GetComponent<RectTransform>();
            if (rectTransform == null || rectTransform.parent == null)
                return;
            
            var parent = rectTransform.parent.GetComponent<RectTransform>();
            
            Debug.Log($"{parent.name} resize with closest image {image.name}");
            
            Undo.RecordObject(parent, "NiceButton Resize");
            parent.sizeDelta = image.sprite.rect.size;
        }
        #endif
    }
}