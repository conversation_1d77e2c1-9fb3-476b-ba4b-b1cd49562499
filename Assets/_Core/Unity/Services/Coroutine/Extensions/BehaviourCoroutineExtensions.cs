// SPDX-License-Identifier: MIT
// Copyright (c) 2016-2021 <PERSON> (@JuDelCo)

#if UNITY_2019_3_OR_NEWER

using System.Collections;
using OnePuz.Handlers;
using OnePuz.Services.Internal;
using UnityEngine;

namespace OnePuz.Extensions
{
	public static class BehaviourCoroutineExtensions
	{
		public static Services.Coroutine CoroutineStart(this Behaviour behaviour, IEnumerator routine, bool alwaysActive = true)
		{
			return ServiceCache.Coroutine.StartCoroutine(new BehaviourLinkHandler(behaviour, alwaysActive), routine);
		}
	}
}

#endif
