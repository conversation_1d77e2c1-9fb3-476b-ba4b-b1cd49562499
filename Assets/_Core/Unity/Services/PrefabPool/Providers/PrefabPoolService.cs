// SPDX-License-Identifier: MIT
// Copyright (c) 2016-2021 <PERSON> (@JuDelCo)

#if UNITY_2019_3_OR_NEWER

using System.Collections.Generic;
using System.Linq;
using OnePuz.Extensions;
using OnePuz.Util;
using UnityEngine;
using Prefab = UnityEngine.GameObject;
using GameObject = UnityEngine.GameObject;

namespace OnePuz.Services
{
	public class GlobalPrefabPoolService : PrefabPoolService { }
	public class ScenePrefabPoolService : PrefabPoolService { }
	
	public class PrefabPoolService : IPrefabPoolService, IServiceLoad, IServiceUnload
	{
		[System.Serializable] internal class GameObjectList : List<GameObject> { }
		[System.Serializable] internal class PrefabPools : SerializableDictionary<Prefab, GameObjectList> { }

		private GameObject container;
		[SerializeReference]
		private readonly PrefabPools pools = new();

		public void Load()
		{
			if (container is null)
			{
				container = new GameObject(GetType().Name.Replace("Service", ""))
				{
					hideFlags = HideFlags.NotEditable
				};
				container.SetActive(false);
				Object.DontDestroyOnLoad(container);
			}
		}

		public void Unload()
		{
			ClearAll(true);
			Object.Destroy(container);
		}

		public void SetCapacity(Prefab prefab, int capacity)
		{
			pools.GetOrInsertNew(prefab).Capacity = capacity;
		}

		public void WarmupCapacity(Prefab prefab, int capacity)
		{
			var pool = pools.GetOrInsertNew(prefab);
			
			for (int i = pool.Count; i < capacity; ++i)
			{
				var target = Object.Instantiate(prefab, container.transform, false);
				target.SetActive(false);
				pool.Add(target);
			}
		}

		public GameObject Spawn(Prefab prefab)
		{
			return Spawn(prefab, null, Vector3.zero, Quaternion.identity);
		}

		public GameObject Spawn(Prefab prefab, Transform parent)
		{
			return Spawn(prefab, parent, Vector3.zero, Quaternion.identity);
		}

		public GameObject Spawn(Prefab prefab, Vector3 position, Quaternion rotation)
		{
			return Spawn(prefab, null, position, rotation);
		}

		public GameObject Spawn(Prefab prefab, Transform parent, Vector3 position, Quaternion rotation)
		{
			var pool = pools.GetOrInsertNew(prefab);
			var target = pool.Find(go => !go.activeInHierarchy && !go.activeSelf);

			if (target is null)
			{
				target = Object.Instantiate(prefab);
				pool.Add(target);
			}

			target.transform.SetParent(parent, false);
			target.transform.position = position;
			target.transform.rotation = rotation;

			if (parent is null)
			{
				UnityEngine.SceneManagement.SceneManager.MoveGameObjectToScene(target, UnityEngine.SceneManagement.SceneManager.GetActiveScene());
			}

			target.SetActive(true);

			return target;
		}

		public void Recycle(GameObject target)
		{
			List<GameObject> pool = null;

			foreach (var kvp in pools)
			{
				var prefabPool = kvp.Value;

				if (prefabPool.Contains(target))
				{
					pool = prefabPool;
					break;
				}
			}

			if (pool != null)
			{
				target.SetActive(false);
				target.transform.SetParent(container.transform, false);
			}
			else
			{
				Object.Destroy(target);
			}
		}

		public void RecycleAll(Prefab prefab)
		{
			if(!pools.TryGetValue(prefab, out var pool))
				return;

			foreach (var go in pool.Where(go => go.activeSelf))
			{
				go.SetActive(false);
				go.transform.SetParent(container.transform, false);
			}
		}

		public void RecycleAll()
		{
			foreach (var kvp in pools)
			{
				foreach (var go in kvp.Value.Where(go => go.activeSelf))
				{
					go.SetActive(false);
					go.transform.SetParent(container.transform, false);
				}
			}
		}

		public int Count(Prefab prefab, bool includeActive = false)
		{
			var total = 0;

			if (pools.ContainsKey(prefab))
			{
				var pool = pools[prefab];

				foreach (var go in pool)
				{
					if (includeActive || (!go.activeInHierarchy && !go.activeSelf))
					{
						++total;
					}
				}

				return pools[prefab].Count;
			}

			return total;
		}

		public int CountAll(bool includeActive = false)
		{
			var total = 0;

			foreach (var kvp in pools)
			{
				total += Count(kvp.Key, includeActive);
			}

			return total;
		}

		public void Shrink(Prefab prefab, int maxSize, bool clearSpawned = false)
		{
			if (!pools.ContainsKey(prefab))
			{
				return;
			}

			var pool = pools[prefab];
			var total = clearSpawned ? pool.Count : Count(prefab);

			pool.ForEachReverse(go =>
			{
				if (total <= maxSize)
				{
					return;
				}

				if (!go.activeInHierarchy && !go.activeSelf)
				{
					Object.Destroy(go);
					pool.Remove(go);
					--total;
				}
			});

			if (clearSpawned && total > maxSize)
			{
				pool.ForEachReverse(go =>
				{
					if (total <= maxSize)
					{
						return;
					}

					Object.Destroy(go);
					pool.Remove(go);
					--total;
				});
			}
		}

		public void Clear(Prefab prefab, bool clearSpawned = false)
		{
			if (!pools.ContainsKey(prefab))
			{
				return;
			}

			var pool = pools[prefab];

			pool.ForEachReverse(go =>
			{
				if (clearSpawned || (!go.activeInHierarchy && !go.activeSelf))
				{
#if UNITY_EDITOR
					if (UnityEditor.EditorApplication.isPlaying)
					{
						Object.Destroy(go);
					}
					else
					{
						Object.DestroyImmediate(go);
					}
#else
						GameObject.Destroy(go);
#endif

					pool.Remove(go);
				}
			});
		}

		public void ClearAll(bool clearSpawned = false)
		{
			foreach (var kvp in pools)
			{
				Clear(kvp.Key, clearSpawned);
			}
		}
	}
}

#endif
