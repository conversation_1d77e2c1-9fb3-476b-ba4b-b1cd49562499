// SPDX-License-Identifier: MIT
// Copyright (c) 2016-2021 <PERSON> (@JuDelCo)

#if UNITY_2019_3_OR_NEWER

using UnityEngine;
using Prefab = UnityEngine.GameObject;
using GameObject = UnityEngine.GameObject;

namespace OnePuz.Services
{
	public interface IPrefabPoolService
	{
		GameObject Spawn(Prefab prefab, Transform parent);
		GameObject Spawn(Prefab prefab, Transform parent, Vector3 position, Quaternion rotation);
		GameObject Spawn(Prefab prefab, Vector3 position, Quaternion rotation);
		GameObject Spawn(Prefab prefab);
		void Recycle(GameObject target);
		void RecycleAll(Prefab target);
		void RecycleAll();

		int Count(Prefab prefab, bool includeActive = false);
		int CountAll(bool includeActive = false);

		void SetCapacity(Prefab prefab, int capacity);
		void WarmupCapacity(Prefab prefab, int capacity);
		void Shrink(Prefab prefab, int maxSize, bool clearSpawned = false);
		void Clear(Prefab prefab, bool clearSpawned = false);
		void ClearAll(bool clearSpawned = false);
	}
}

#endif
