// SPDX-License-Identifier: MIT
// Copyright (c) 2016-2021 <PERSON> (@JuDelCo)

#if UNITY_2019_3_OR_NEWER

using UnityEngine.SceneManagement;

namespace OnePuz.Services
{
	public struct UnitySceneLoadedEvent
	{
		public int sceneIndex;
		public string sceneName;
		public LoadSceneMode loadMode;

		public UnitySceneLoadedEvent(int sceneIndex, string sceneName, LoadSceneMode loadMode)
		{
			this.sceneIndex = sceneIndex;
			this.sceneName = sceneName;
			this.loadMode = loadMode;
		}
	}
}

#endif
