using System;
using UnityEngine;

namespace OnePuz.Attributes
{
    [AttributeUsage(AttributeTargets.Field, AllowMultiple = true)]
    public class DropDownValueOfAttribute : PropertyAttribute
    {
        public readonly string[] fieldNames;
        public readonly string[] fieldValues;
        public readonly string[] fieldNameFlags;

        public DropDownValueOfAttribute(Type type)
        {
            var fieldInfos = type.GetFields();
            fieldNames = new string[fieldInfos.Length];
            fieldValues = new string[fieldInfos.Length];
            fieldNameFlags = new string[fieldInfos.Length];
            for (int i = 0; i < fieldInfos.Length; i++)
            {
                fieldNames[i] = fieldInfos[i].Name;
                fieldNameFlags[i] = fieldInfos[i].Name;
                fieldValues[i] = fieldInfos[i].GetRawConstantValue().ToString();
            }

            fieldNameFlags[^1] = "None";
        }
    }
}