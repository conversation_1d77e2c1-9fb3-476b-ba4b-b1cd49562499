using System;
using PrimeTween;
using Sirenix.OdinInspector;
using UnityEngine;

namespace _FeatureHub.PrimeTweenExtra
{
    public class TweenAutomatic : MonoBehaviour
    {
        private enum PlayType
        {
            OnAwake, OnEnable
        }
        private enum TweenType
        {
            UIAnchored, UIAnchoredX, UIAnchoredY, Scale, LocalRotation
        }

        [SerializeField] private PlayType m_PlayType = PlayType.OnAwake;
        [SerializeField] private TweenType m_Type = TweenType.UIAnchored;

        #region Parameter Customize
        
        [SerializeField, LabelText("startValue"), ShowIf("@m_Type == TweenType.LocalRotation"), BoxGroup("UI Anchored")]
        private Vector3 m_StartVector3;
        [SerializeField, LabelText("endValue"), ShowIf("@m_Type == TweenType.LocalRotation"), BoxGroup("UI Anchored")]
        private Vector3 m_EndVector3;

        [SerializeField, LabelText("startValue"), ShowIf("@m_Type == TweenType.UIAnchored"), BoxGroup("UI Anchored")]
        private Vector2 m_StartVector2;
        [SerializeField, LabelText("endValue"), ShowIf("@m_Type == TweenType.UIAnchored"), BoxGroup("UI Anchored")]
        private Vector2 m_EndVector2;
        
        [SerializeField, LabelText("startValue"), ShowIf("@m_Type == TweenType.UIAnchoredX || m_Type == TweenType.UIAnchoredY || m_Type == TweenType.Scale"), BoxGroup("UI Anchored")]
        private float m_StartFloat;
        [SerializeField, LabelText("endValue"), ShowIf("@m_Type == TweenType.UIAnchoredX || m_Type == TweenType.UIAnchoredY || m_Type == TweenType.Scale"), BoxGroup("UI Anchored")]
        private float m_EndFloat;
        
        #endregion
        
        [SerializeField] private Ease m_Ease = Ease.Default;
        [SerializeField] private CycleMode m_CycleMode = CycleMode.Restart;
        [SerializeField] private int m_Loop = 1;
        [SerializeField] private float m_Duration = 1;
        
        private RectTransform m_Rect;
        
        
        private void Awake()
        {
            if(m_PlayType != PlayType.OnAwake) return;
            Execute();

        }

        private void OnEnable()
        {
            if(m_PlayType != PlayType.OnEnable) return;
            Execute();
        }

        private void OnDisable()
        {
            if (m_Rect != null) Tween.StopAll(m_Rect);
        }

        private Tween Execute()
        {
            return m_Type switch
            {
                TweenType.UIAnchored => Tween.UIAnchoredPosition(m_Rect ??= GetComponent<RectTransform>(), m_StartVector2, m_EndVector2, m_Duration, m_Ease, m_Loop, m_CycleMode),
                TweenType.UIAnchoredX => Tween.UIAnchoredPositionX(m_Rect ??= GetComponent<RectTransform>(), m_StartFloat, m_EndFloat, m_Duration, m_Ease, m_Loop, m_CycleMode),
                TweenType.UIAnchoredY => Tween.UIAnchoredPositionY(m_Rect ??= GetComponent<RectTransform>(), m_StartFloat, m_EndFloat, m_Duration, m_Ease, m_Loop, m_CycleMode),
                TweenType.Scale => Tween.Scale(m_Rect ??= GetComponent<RectTransform>(), m_StartFloat, m_EndFloat, m_Duration, m_Ease, m_Loop, m_CycleMode),
                TweenType.LocalRotation => Tween.LocalRotation(m_Rect ??= GetComponent<RectTransform>(), m_StartVector3, m_EndVector3, m_Duration, m_Ease, m_Loop, m_CycleMode),
                _ => throw new ArgumentOutOfRangeException()
            };
        }
    }
}