using System.Linq;
using System.Reflection;
using OnePuz.Data;
using OnePuz.Definition;
using OnePuz.Services;
using UnityEngine;

namespace OnePuz.Data.Services
{
    public class DataService : MonoBehaviour, IServiceLoad
    {
        #region Game Data

        private GameData _gameData;
        public GameData GameData => _gameData;

        #endregion

        public void Load()
        {
            OLogger.Log($"Load");

            _gameData = Load<GameData>("GameData");
        }

        private T Load<T>(string key) where T : class, new()
        {
            var data = ES3.KeyExists(key) ? ES3.Load<T>(key) : new T();
            if (data == null) data = new T();
            return data;
        }

        public T GetData<T>(string key) where T : ISaveData, IVersionedData, new()
        {
            if (!GameData.data.TryGetValue(key, out var container))
            {
                container = new SaveDataContainer();
                GameData.data[key] = container;
            }

            var data = container.GetData<T>();
            data.Migrate();
            return data;
        }

        private void Save()
        {
            foreach (var pair in GameData.data)
            {
                pair.Value.Save();
            }

            ES3.Save("GameData", GameData);
            OLogger.LogNotice("Save");
        }

        private void OnApplicationPause(bool isPaused)
        {
            if (isPaused)
            {
                Save();
            }
        }

        private void OnDestroy()
        {
            Save();
        }
    }
}