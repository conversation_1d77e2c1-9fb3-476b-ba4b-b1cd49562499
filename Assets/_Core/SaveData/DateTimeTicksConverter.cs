using System;
using Newtonsoft.Json;

namespace OnePuz.Data
{
    public class DateTimeTicksConverter : JsonConverter<DateTime>
    {
        public override void Write<PERSON><PERSON>(JsonWriter writer, DateTime value, JsonSerializer serializer)
        {
            writer.WriteValue(value.Ticks);
        }

        public override DateTime ReadJson(JsonReader reader, Type objectType, DateTime existingValue, bool hasExistingValue, JsonSerializer serializer)
        {
            var ticks = (long)reader.Value;
            return new DateTime(ticks);
        }
    }
}