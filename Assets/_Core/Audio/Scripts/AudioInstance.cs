using PrimeTween;
using UnityEngine;
using UnityEngine.Audio;
using UnityEngine.Events;

namespace OnePuz.Audio
{
    public class AudioInstance
    {
        public AudioPooler.AudioPool pMyPool { get; private set; }
        public AudioSource pAudioSource { get; private set; }

        private UnityAction _timeOutCallback;

        public bool pIsPlaying => pAudioSource.isPlaying;

        public AudioInstance SetPooler(AudioPooler.AudioPool pool)
        {
            pMyPool = pool;
            return this;
        }

        public AudioInstance SetAudioSource(AudioSource audioSource)
        {
            this.pAudioSource = audioSource;
            return this;
        }

        public AudioInstance SetClip(AudioClip clip, bool loop, float pitch)
        {
            pAudioSource.clip = clip;
            pAudioSource.loop = loop;
            pAudioSource.pitch = pitch;
            return this;
        }

        public AudioInstance SetOutput(AudioMixerGroup mixerGroup)
        {
            pAudioSource.outputAudioMixerGroup = mixerGroup;
            return this;
        }

        public AudioInstance Play(float volume = 1)
        {
            pAudioSource.volume = volume;
            pAudioSource.time = 0;
            pAudioSource.enabled = true;
            pAudioSource.Play();
            if (!pAudioSource.loop)
                Core.Audio.pAudioUpdate += OnDespawn;
            return this;
        }

        public AudioInstance PlayAtPosition(float volume = 1, float playbackPosition = 0)
        {
            pAudioSource.volume = volume;
            pAudioSource.time = playbackPosition;
            pAudioSource.enabled = true;
            pAudioSource.Play();
            if (!pAudioSource.loop)
                Core.Audio.pAudioUpdate += OnDespawn;
            return this;
        }

        public void Stop()
        {
            Core.Audio.pAudioUpdate -= OnDespawn;
            pAudioSource.Stop();
            AudioPooler.Despawn(this);
        }

        public void Stop(float time) => Tween.Delay(this, time, @this => @this.Stop());

        public float GetPlaybackPosition() => pAudioSource.time;

        public AudioInstance FadeIn(float volume = 1)
        {
            pAudioSource.volume = 0;
            pAudioSource.time = 0;
            pAudioSource.enabled = true;
            pAudioSource.Play();
            if (!pAudioSource.loop)
                Core.Audio.pAudioUpdate += OnDespawn;
            Tween.AudioVolume(pAudioSource, volume, 1);
            return this;
        }

        public AudioInstance FadeOut(UnityAction complete, float volume = 1)
        {
            Tween.AudioVolume(pAudioSource, 0, 1)
                .OnComplete(this, @this =>
                {
                    @this.Stop();
                    complete.Invoke();
                });
            return this;
        }

        public void ChangeVolume(float volumeTarget) => Tween.AudioVolume(pAudioSource, volumeTarget, 1);

        private void OnDespawn()
        {
            if (pAudioSource && !pAudioSource.isPlaying)
            {
                AudioPooler.Despawn(this);
                _timeOutCallback?.Invoke();
                Core.Audio.pAudioUpdate -= OnDespawn;
            }
        }

        public AudioInstance SetTimeOutCallback(UnityAction callback)
        {
            this._timeOutCallback = callback;
            return this;
        }
    }
}