using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace OnePuz.Audio
{
    public partial class AudioKey
    {
        public const string Click = "Sounds/Click";
        
        public static List<string> GetAllAudioKeys()
        {
            var fields = typeof(AudioKey).GetFields(BindingFlags.Public | BindingFlags.Static | BindingFlags.FlattenHierarchy)
                .Where(fi => fi.IsLiteral && !fi.IsInitOnly);

            var values = new List<string>();
            foreach (var field in fields)
            {
                Console.WriteLine($"{field.Name} = {field.GetRawConstantValue()}");
                values.Add(field.GetRawConstantValue().ToString());
            }

            return values;
        }
    }
}