using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Reflection;
using System.Text;
using _FeatureHub.Attributes.Core;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;
using UnityEngine.SceneManagement;
using Object = UnityEngine.Object;

namespace _FeatureHub.Attributes.Editor
{
    [SuppressMessage("ReSharper", "AccessToStaticMemberViaDerivedType")]
    public static class ReferenceValueUtilities
    {
        [Flags]
        public enum AutomaticFlag : byte
        {
            None = 0,
            CurrentPrefab = 1,
            CurrentScene = 1 << 1,
            AllScenes = 1 << 2,
            AllPrefabs = 1 << 3
        }
        
        private const string MENU_AUTOMATIC_CURRENT_PREFAB = "Tools/🔗 Reference Value In Background/Opening Prefab";
        private const string MENU_AUTOMATIC_CURRENT_SCENE = "Tools/🔗 Reference Value In Background/Active Scene";
        private const string MENU_AUTOMATIC_OTHER_SCENES = "Tools/🔗 Reference Value In Background/All Other Scenes";
        private const string MENU_AUTOMATIC_ALL_PREFABS = "Tools/🔗 Reference Value In Background/All Prefabs";
        private const string MENU_QUERY_IN_CLASS = "CONTEXT/MonoBehaviour/🔗 Reference Value/In This Class";
        private const string MENU_QUERY_IN_OBJECT = "CONTEXT/MonoBehaviour/🔗 Reference Value/All Classes In Object";

        public static AutomaticFlag automaticFlag
        {
            get => (AutomaticFlag)EditorPrefs.GetInt($"editor:{nameof(AutomaticFlag)}", (int)AutomaticFlag.None);
            private set => EditorPrefs.SetInt($"editor:{nameof(AutomaticFlag)}", (int)value);
        }

        #region ───────────────────────────────[Menu Tools]───────────────────────────────

        private static bool ToggleFlag(AutomaticFlag value)
        {
            var flag = automaticFlag;
            if (flag.HasFlag(value))
                flag &= ~value;
            else
                flag |= value;
            automaticFlag = flag;
            return flag.HasFlag(value);
        }
        
        [MenuItem(MENU_AUTOMATIC_CURRENT_PREFAB, priority = 20)]
        private static void AutoAllCurrentPrefab()
        {
            if(ToggleFlag(AutomaticFlag.CurrentPrefab))
                QueryCurrentPrefab();
        }

        [MenuItem(MENU_AUTOMATIC_CURRENT_PREFAB, true)]
        private static bool ValidateAutoCurrentPrefab()
        {
            Menu.SetChecked(MENU_AUTOMATIC_CURRENT_PREFAB, automaticFlag.HasFlag(AutomaticFlag.CurrentPrefab));
            return true;
        }


        [MenuItem(MENU_AUTOMATIC_CURRENT_SCENE, priority = 21)]
        private static void AutoAllCurrentScene()
        {
            if(ToggleFlag(AutomaticFlag.CurrentScene))
                QueryCurrentScenes();
        }

        [MenuItem(MENU_AUTOMATIC_CURRENT_SCENE, true)]
        private static bool ValidateAutoCurrentScene()
        {
            Menu.SetChecked(MENU_AUTOMATIC_CURRENT_SCENE, automaticFlag.HasFlag(AutomaticFlag.CurrentScene));
            return true;
        }
        
        [MenuItem(MENU_AUTOMATIC_OTHER_SCENES, priority = 99)]
        private static void AutoAllScenes()
        {
            if (ToggleFlag(AutomaticFlag.AllScenes))
                QueryAllScenes();
        }

        [MenuItem(MENU_AUTOMATIC_OTHER_SCENES, true)]
        private static bool ValidateAutoAllScenes()
        {
            Menu.SetChecked(MENU_AUTOMATIC_OTHER_SCENES, automaticFlag.HasFlag(AutomaticFlag.AllScenes));
            return true;
        }
        
        [MenuItem(MENU_AUTOMATIC_ALL_PREFABS, priority = 100)]
        private static void AutoAllPrefabs()
        {
            if (ToggleFlag(AutomaticFlag.AllPrefabs))
                QueryAllPrefabs();
        }

        [MenuItem(MENU_AUTOMATIC_ALL_PREFABS, true)]
        private static bool ValidateAutoAllPrefabs()
        {
            Menu.SetChecked(MENU_AUTOMATIC_ALL_PREFABS, automaticFlag.HasFlag(AutomaticFlag.AllPrefabs));
            return true;
        }
        
        #endregion

        #region ───────────────────────────────[Methods]──────────────────────────────────
        
        [SuppressMessage("ReSharper", "PossibleMultipleEnumeration")]
        [SuppressMessage("ReSharper", "UseMethodAny.2")]
        private static bool AssignReferenceValue(MonoBehaviour target, bool isAutomatic = true)
        {
            var type = target.GetType();
            if (isAutomatic && !type.IsDefined(typeof(ReferenceInBackground)))
            {
                return false;
            }

            var isModified = false;
            var fieldInfos = new List<FieldInfo>();
            var currentType = type;
            while (currentType != null && currentType.IsSubclassOf(typeof(MonoBehaviour)))
            {
                fieldInfos.AddRange(currentType.GetFields(BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic));
                currentType = currentType.BaseType;
            }
            
            foreach (var field in fieldInfos)
            {
                if (!field.IsDefined(typeof(ReferenceValueAttribute)))
                    continue;
                var attribute = field.GetCustomAttribute<ReferenceValueAttribute>();
                if(attribute.manually)
                    continue;

                using var serializedObject = new SerializedObject(target);
                serializedObject.Update();
                if (!field.FieldType.IsSerializable)
                {
                    var currentValue = field.GetValue(target);
                    var targetValue = attribute.GetAssignedValue(target, field);

                    if (targetValue is null)
                    {
                        Debug.LogWarning($"targetValue is null for field <b>{field.Name}</b>");
                        continue;
                    }
                    
                    if (currentValue is not null && !currentValue.Equals(null) && currentValue.GetType() == targetValue.GetType())
                    {
                        if (currentValue.GetHashCode() != targetValue.GetHashCode())
                            Debug.LogWarning(
                                $"Conflict at <b>{target.GetType().Name}.{field.Name}</b>: The current value <b>({currentValue})</b> is not the same as target value <b>({targetValue.name})</b>. Recheck reference path as param in <i>ReferenceValueAttribute</i>.");
                        continue;
                    }
                    isModified = true;
                    field.SetValue(target, targetValue);
                    serializedObject.ApplyModifiedProperties();
                    continue;
                }

                var method = default(MethodInfo);
                currentType = type;
                while (currentType != null && currentType.IsSubclassOf(typeof(MonoBehaviour)))
                {
                    method = currentType.GetMethod(attribute.relativePath, BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic);
                    if(method != null) break;
                    currentType = currentType.BaseType;
                }
                
                if(method is null)
                    continue;

                if (method.Invoke(target, null) is not IEnumerable<object> targetEnumerable || targetEnumerable.Count() == 0)
                {
                    Debug.LogWarning($"Conflict at <b>{target.GetType().Name}.{field.Name}</b>: <b>{field.FieldType.Name}</b> is null or has count == 0. Recheck reference path as param in <i>ReferenceValueAttribute</i>.");
                    continue;
                }

                if (field.GetValue(target) is IEnumerable<object> currentEnumerable && currentEnumerable.Count() != 0)
                {
                    if (currentEnumerable.Count() != targetEnumerable.Count())
                    {
                        Debug.LogWarning(
                            $"Conflict at <b>{target.GetType().Name}.{field.Name}</b>: Current {field.Name} has count is different from Target {field.Name} count. Recheck this <i>ReferenceValueAttribute</i> and field info on inspector.");
                        continue;
                    }

                    var templateIndex = 0;
                    var differenceIndies = new List<int>(); 
                    using var currentEnumerator = currentEnumerable.GetEnumerator();
                    using var targetEnumerator = targetEnumerable.GetEnumerator();
                    var currentNext = currentEnumerator.MoveNext();
                    var targetNext = targetEnumerator.MoveNext();
                    while (currentNext && targetNext)
                    {
                        if (!currentEnumerator.Current.GetType().IsSerializable)
                        {
                            if (!currentEnumerator.Current.Equals(targetEnumerator.Current))
                                differenceIndies.Add(templateIndex);
                        }
                        else
                        {
                            var currentFields = currentEnumerator.Current.GetType().GetFields(BindingFlags.Public | BindingFlags.Instance | BindingFlags.NonPublic);
                            var targetFields = targetEnumerator.Current.GetType().GetFields(BindingFlags.Public | BindingFlags.Instance | BindingFlags.NonPublic);
                            for (var index = 0; index < currentFields.Length; index++)
                            {
                                if (currentFields[index].GetValue(currentEnumerator.Current).Equals(targetFields[index].GetValue(targetEnumerator.Current)))
                                    continue;
                                differenceIndies.Add(templateIndex);
                                break;
                            }
                        }

                        currentNext = currentEnumerator.MoveNext();
                        targetNext = targetEnumerator.MoveNext();
                        templateIndex++;
                    }
                    
                    if (differenceIndies.Count > 0)
                    {
                        var builder = new StringBuilder();
                        foreach (var i in differenceIndies)
                            builder.Append($"<b>{i}</b>, ");
                        builder.Remove(builder.Length - 2, 2);
                        Debug.LogWarning($"Conflict at <b>{target.GetType().Name}.{field.Name}</b>: Current value and target value has different at index {builder}. Recheck this <i>ReferenceValueAttribute</i> and field info on inspector.");
                    }
                    
                    continue;
                }
                
                isModified = true;
                field.SetValue(target, targetEnumerable);
                serializedObject.ApplyModifiedProperties();
            }

            return isModified;
        }

        public static void QueryCurrentPrefab()
        {
            var prefab = PrefabStageUtility.GetCurrentPrefabStage();
            if(prefab is null) return;
            
            var isModified = false;
            var monoBehaviours = prefab.prefabContentsRoot.GetComponentsInChildren<MonoBehaviour>();
            foreach (var target in monoBehaviours)
            {
                if(!AssignReferenceValue(target))
                    continue;
                isModified = true;
            }

            if (isModified)
                PrefabUtility.SaveAsPrefabAsset(prefab.prefabContentsRoot, prefab.assetPath);
        }

        public static void QueryCurrentScenes()
        {
            var scene = EditorSceneManager.GetActiveScene();
            ModifiedScene(scene, true);
        }
        
        public static void QueryAllScenes()
        {
            var guids = AssetDatabase.FindAssets("t:scene", new[] { "Assets" });
            var activeScenePath = EditorSceneManager.GetActiveScene().path;
            foreach (var entry in guids)
            {
                var path = AssetDatabase.GUIDToAssetPath(entry);
                if(path.Equals(activeScenePath, StringComparison.Ordinal))
                    continue;
                ModifiedScene(EditorSceneManager.OpenScene(path, OpenSceneMode.Additive), false);
            }
        }
        
        public static void QueryAllPrefabs()
        {
            var guids = AssetDatabase.FindAssets("t:prefab", new[] { "Assets" });
            foreach (var entry in guids)
            {
                var path = AssetDatabase.GUIDToAssetPath(entry);
                var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                var isModified = false; 
                var monoBehaviours = prefab.GetComponents<MonoBehaviour>();
                foreach (var target in monoBehaviours)
                {
                    if(!AssignReferenceValue(target))
                        continue;
                    isModified = true;
                }

                if (isModified)
                    PrefabUtility.SaveAsPrefabAsset(prefab, path);
            }
        }
        
        private static void ModifiedScene(Scene scene, bool isActiveScene)
        {
            var rootObjects = scene.GetRootGameObjects();
            foreach (var obj in rootObjects)
            {
                var monoBehaviours = obj.GetComponentsInChildren<MonoBehaviour>();
                Debug.Assert(monoBehaviours != null, $"{nameof(monoBehaviours)} is null.");
                foreach (var target in monoBehaviours)
                {
                    if(!AssignReferenceValue(target))
                        continue;
                    if (PrefabUtility.IsPartOfPrefabInstance(target.gameObject))
                        PrefabUtility.SaveAsPrefabAssetAndConnect(target.gameObject, PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(target), InteractionMode.UserAction);
                }
            }

            EditorSceneManager.MarkSceneDirty(scene);
            EditorSceneManager.SaveScene(scene);
            if (isActiveScene) return;
            EditorSceneManager.CloseScene(scene, true);
        }
        
        #endregion

        #region ──────────────────────────────[Menu Context]──────────────────────────────

        [MenuItem(MENU_QUERY_IN_CLASS, priority = 99)]
        private static void QueryInClass(MenuCommand command)
        {
            var target = command.context as MonoBehaviour;
            Debug.Assert(target != null, $"{nameof(target)} is null.");
            if(!AssignReferenceValue(target, false))
                return;
            EditorSceneManager.MarkSceneDirty(EditorSceneManager.GetActiveScene());
            if (PrefabUtility.IsPartOfPrefabInstance(target.gameObject))
            {
                PrefabUtility.SaveAsPrefabAssetAndConnect(target.gameObject, PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(target), InteractionMode.AutomatedAction);
                return;
            }

            if (PrefabUtility.IsPartOfPrefabAsset(target.gameObject))
            {
                PrefabUtility.SavePrefabAsset(target.gameObject);
                return;
            }

            var stage = PrefabStageUtility.GetPrefabStage(target.gameObject);
            var prefabRoot = stage?.prefabContentsRoot;
            if (prefabRoot is not null)
                PrefabUtility.SaveAsPrefabAsset(prefabRoot, stage.assetPath);
        }
        
        // [MenuItem(MENU_QUERY_IN_CLASS, true)]
        // private static bool ValidateQueryInClass() => automaticFlag == AutomaticFlag.None;

        [MenuItem(MENU_QUERY_IN_OBJECT, priority = 100)]
        private static void QueryInObject(MenuCommand command)
        {
            var monoBehaviours = (command.context as Component)?.GetComponents<MonoBehaviour>();
            Debug.Assert(monoBehaviours != null, $"{nameof(monoBehaviours)} is null.");
            foreach (var target in monoBehaviours)
            {
                if(!AssignReferenceValue(target, false))
                    continue;
                EditorSceneManager.MarkSceneDirty(EditorSceneManager.GetActiveScene());
                if (PrefabUtility.IsPartOfPrefabInstance(target.gameObject))
                {
                    PrefabUtility.SaveAsPrefabAssetAndConnect(target.gameObject, PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(target), InteractionMode.AutomatedAction);
                    continue;
                }

                if (PrefabUtility.IsPartOfPrefabAsset(target.gameObject))
                {
                    PrefabUtility.SavePrefabAsset(target.gameObject);
                    continue;
                }

                var stage = PrefabStageUtility.GetPrefabStage(target.gameObject);
                var prefabRoot = stage?.prefabContentsRoot;
                if (prefabRoot is not null)
                    PrefabUtility.SaveAsPrefabAsset(prefabRoot, stage.assetPath);
            }
        }

        // [MenuItem(MENU_QUERY_IN_OBJECT, true)]
        // private static bool ValidateQueryInObject() => automaticFlag == AutomaticFlag.None;

        #endregion
    }
}