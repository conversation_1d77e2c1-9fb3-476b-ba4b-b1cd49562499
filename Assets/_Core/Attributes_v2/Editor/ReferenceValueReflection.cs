using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;

namespace _FeatureHub.Attributes.Editor
{
    using static ReferenceValueUtilities;
    using static ReferenceValueUtilities.AutomaticFlag;
    [InitializeOnLoad]
    public static class ReferenceValueReflection
    {
        static ReferenceValueReflection()
        {
            EditorApplication.delayCall -= HandleInspectorUpdate;
            EditorApplication.delayCall += HandleInspectorUpdate;
        }

        private static void HandleInspectorUpdate()
        {
            EditorApplication.delayCall -= HandleInspectorUpdate;
            HandleReferenceValues();
        }
        
        private static void HandleReferenceValues()
        {
            if(EditorApplication.isPlayingOrWillChangePlaymode)
                return;
            if (automaticFlag.HasFlag(CurrentPrefab)) QueryCurrentPrefab();
            if (automaticFlag.HasFlag(CurrentScene)) QueryCurrentScenes();
            if (automaticFlag.HasFlag(AllScenes)) QueryAllScenes();
            if (automaticFlag.HasFlag(AllScenes)) QueryAllPrefabs();
        }
    }
}