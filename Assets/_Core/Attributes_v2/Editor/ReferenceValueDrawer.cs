using System;
using _FeatureHub.Attributes.Core;
using _FeatureHub.Attributes.Definition;
using Sirenix.OdinInspector.Editor;
using UnityEditor;
using UnityEngine;

namespace _FeatureHub.Attributes.Editor
{
    [CustomPropertyDrawer(typeof(ReferenceValueAttribute))]
    public class ReferenceValueDrawer : PropertyDrawer
    {
        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            property.serializedObject.Update();
            
            switch (property.propertyType)
            {
                case SerializedPropertyType.Generic:
                    EditorGUI.BeginDisabledGroup(true);
                    EditorGUI.PropertyField(position, property, label, true);
                    EditorGUILayout.Space((property.CountInProperty() - 1) * (EditorGUIUtility.singleLineHeight + EditorGUIUtility.standardVerticalSpacing));
                    EditorGUI.EndDisabledGroup();
                    property.serializedObject.ApplyModifiedProperties();
                    return;
                case SerializedPropertyType.ObjectReference:
                    break;
                default:
                    assertNotSupport();
                    property.serializedObject.ApplyModifiedProperties();
                    return;
            }

            var targetAttribute = (ReferenceValueAttribute)attribute;
            if (!targetAttribute.manually)
            {
                var isInlineDrew = drawInlineEditor();
                EditorGUI.BeginDisabledGroup(property.objectReferenceValue is not null);
                EditorGUI.ObjectField(!isInlineDrew
                        ? position
                        : new Rect(position) { width = position.width - EditorGUIUtility.singleLineHeight, x = position.x + EditorGUIUtility.singleLineHeight }
                    , property
                    , label);
                EditorGUI.EndDisabledGroup();
                property.serializedObject.ApplyModifiedProperties();
            }
            else
            {
                if (property.objectReferenceValue is null)
                {
                    EditorGUI.ObjectField(new Rect(position) { width = position.width - 100 - 2 }, property, label);

                    if (targetAttribute.defaultReference)
                    {
                        drawAssignButton("As Default");
                        property.serializedObject.ApplyModifiedProperties();
                        return;
                    }
                    
                    switch (targetAttribute.specifiedTarget.type)
                    {
                        case SpecifiedType.None:
                            drawAssignButton("Assign");
                            if(!string.IsNullOrEmpty(targetAttribute.relativePath))
                                drawSubTitle(targetAttribute.relativePath);
                            break;
                        case SpecifiedType.Field:
                        case SpecifiedType.Local:
                        case SpecifiedType.Global:
                            if (targetAttribute.GetSpecifiedTarget((Component)getSerializedTargetObject()) is null)
                            {
                                EditorGUI.BeginDisabledGroup(true);
                                drawAssignButton("Assign", true);
                                EditorGUI.EndDisabledGroup();
                                drawErrorBox(targetAttribute.specifiedTarget.type switch
                                {
                                    SpecifiedType.Field => $"'{targetAttribute.specifiedTarget.name}' is missing.",
                                    SpecifiedType.Local or SpecifiedType.Global => $"'{targetAttribute.specifiedTarget.name}' is not exist in hierarchy.",
                                    _ => throw new ArgumentOutOfRangeException()
                                });
                            }
                            else
                            {
                                drawAssignButton("Assign");
                                if(!string.IsNullOrEmpty(targetAttribute.relativePath))
                                    drawSubTitle($"<color=#1361AE>{targetAttribute.specifiedTarget.name}</color>/{targetAttribute.relativePath}");
                            }

                            break;
                        default:
                            throw new ArgumentOutOfRangeException();
                    }
                }
                else
                {
                    var isInlineDrew = drawInlineEditor();
                    EditorGUI.BeginDisabledGroup(true);
                    EditorGUI.ObjectField(!isInlineDrew
                            ? new Rect(position) { width = position.width - 100 - 2 }
                            : new Rect(position) { width = position.width - EditorGUIUtility.singleLineHeight - 100, x = position.x + EditorGUIUtility.singleLineHeight }
                        , property
                        , label);
                    EditorGUI.EndDisabledGroup();
                    
                    GUI.color = PresetGUIColor.darkWhite;
                    if (GUI.Button(new Rect(position) { width = 100 - 2, x = position.x + position.width - 100 + 2 }, "Fallback"))
                    {
                        property.objectReferenceValue = null;
                        property.serializedObject.ApplyModifiedProperties();
                    }
                    GUI.color = Color.white;
                }

                property.serializedObject.ApplyModifiedProperties();

                return;

                UnityEngine.Object getSerializedTargetObject()
                {
                    return property.serializedObject.targetObject;
                }

                void getAssignedValue()
                {
                    property.objectReferenceValue = targetAttribute.GetAssignedValue((Component)getSerializedTargetObject(), fieldInfo);
                }

                void drawAssignButton(string labelButton, bool isEmpty = false)
                {
                    GUI.color = isEmpty ? PresetGUIColor.blue :  PresetGUIColor.lightBlue;
                    if (GUI.Button(new Rect(position) { width = 100 - 2, x = position.x + position.width - 100 + 2 }, labelButton) && !isEmpty)
                        getAssignedValue();
                    GUI.color = Color.white;
                }

                void drawSubTitle(string detail)
                {
                    GUILayout.Space(EditorGUIUtility.singleLineHeight);
                    EditorGUI.LabelField(new Rect(position) { y = position.y + EditorGUIUtility.singleLineHeight + 1 }, " ",
                        detail, PresetGUIStyle.subtitle);
                }

                void drawErrorBox(string errorMessage)
                {
                    GUILayout.Space(EditorGUIUtility.singleLineHeight);
                    EditorGUI.HelpBox(new Rect(position)
                        {
                            width = position.width - EditorGUIUtility.labelWidth - 2, x = position.x + EditorGUIUtility.labelWidth + 2,
                            y = position.y + EditorGUIUtility.singleLineHeight + 1
                        }
                        , errorMessage, MessageType.Error);
                }
            }

            return;

            void assertNotSupport()
            {
                EditorGUI.HelpBox(new Rect(position)
                    {
                        width = position.width - EditorGUIUtility.labelWidth - 2,
                        x = position.x + EditorGUIUtility.labelWidth + 2
                    },
                    "This attribute is only valid on object references.", MessageType.Error);
                EditorGUI.LabelField(position, label.text, string.Empty);
            }

            bool drawInlineEditor()
            {
                if (!targetAttribute.inlineEditor)
                    return false;
                if (property.objectReferenceValue is null or GameObject)
                    return false;
                
                if (GUI.Button(new Rect(position) { width = EditorGUIUtility.singleLineHeight - EditorGUIUtility.standardVerticalSpacing }
                        , new GUIContent(EditorGUIUtility.IconContent("console.infoicon").image)
                        , PresetGUIStyle.simpleButton))
                {
                    var window = OdinEditorWindow.InspectObject(property.objectReferenceValue);
                    window.Show();
                }

                return true;
            }
        }
    }
}