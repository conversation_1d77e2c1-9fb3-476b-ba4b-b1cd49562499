using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;
using Object = UnityEngine.Object;

namespace _FeatureHub.Attributes.Extensions
{
    public static class ReferenceValueExtension
    {
        public static Transform FindDeepInHierarchy(this Component source, string name)
        {
            var rootObjects = source.gameObject.scene.GetRootGameObjects();
            foreach (var go in rootObjects)
            {
                if (go.name.Equals(name, StringComparison.Ordinal))
                    return go.transform;
            }

            foreach (var entry in rootObjects)
            {
                Transform result;
                if ((result = entry.transform.FindDeep(name)) is not null)
                    return result;
            }
            
            Debug.Assert(false, $"<b>Find deep failed!</b> No object named <i>\'{name}\'</i> exists in the hierarchy.");
            return null;
        }

        public static bool ExistInScene<T>(this Scene scene, out T result)
        {
            foreach (var go in scene.GetRootGameObjects())
            {
                var array = go.GetComponentsInChildren<T>();
                if (array.Length > 0)
                {
                    result = array[0];
                    return true;
                }
            }
            
            result = default;
            return false;
        }

        public static Transform FindDeep(this Transform source, string name)
        {
            var queue = new Queue<Transform>();
            queue.Enqueue(source);
            while (queue.Count > 0)
            {
                var result = queue.Peek().Find(name);
                if (result is not null)
                {
                    queue.Clear();
                    return result;
                }

                foreach (Transform item in queue.Dequeue())
                    queue.Enqueue(item);
            }
            Debug.LogError($"<b>Find deep failed!</b> Not found object with name <i>\'{name}\'</i> - is child of object <i>\'{source.name}\'</i>.");
            return null;
        }

        private static Transform FindDeep(this Transform source, params string[] names)
        {
            var result = source;
            foreach (var name in names)
            {
                result = result.FindDeep(name);
                if (result is null) return null;
            }

            return result;
        }
        
        public static Object GetObject(this Component root, Type type)
        {
            if (type == typeof(Transform)) return root.transform;
            if (type == typeof(GameObject)) return root.gameObject;
            return typeof(Component).IsAssignableFrom(type) ? root.GetComponent(type) : null;
        }

        public static Object FindByPath(this Transform source, string relativePath, Type type)
        {
            var names = relativePath.Split('/');
            var result = source.FindDeep(names);
            if (result is null) return null;
            if (type == typeof(Transform)) return result;
            if (type == typeof(GameObject)) return result.gameObject;
            return typeof(Component).IsAssignableFrom(type) ? result.GetComponent(type) : null;
        }

        public static bool TryGetRootTransform(this object _, string name, out Transform result)
        {
            var rootObjects = SceneManager.GetActiveScene().GetRootGameObjects();
            foreach (var entry in rootObjects)
            {
                if (string.CompareOrdinal(entry.name, name) != 0) continue;
                result = entry.transform;
                return true;
            }

            result = null;
            return false;
        }
    }
}