using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using UnityEngine;
using Object = UnityEngine.Object;

namespace OnePuz.Extensions
{
    public static class TransformExtensions
    {
        public static Transform FindDeep(this Transform source, string name)
        {
            var queue = new Queue<Transform>();
            queue.Enqueue(source);
            while (queue.Count > 0)
            {
                var result = queue.Peek().Find(name);
                if (result is not null)
                {
                    queue.Clear();
                    return result;
                }
                
                foreach (Transform item in queue.Dequeue())
                    queue.Enqueue(item);
            }
        
            Debug.LogErrorFormat("Error! Not found object with name \"{0}\" - is child of object \"{1}\"", name, source.name);
            return null;
        }
        
        public static Transform FindDeep(this Transform source, params string[] names)
        {
            var result = source;
            foreach (var name in names)
            {
                result = result.FindDeep(name);
                if (result == null) return null;
            }
            
            return result;
        }

        public static T FindDeep<T>(this Transform source, string name) where T : Object => source.FindDeep(name).CastObject<T>();

        public static T FindDeep<T>(this Transform source, params string[] names) where T : Object => source.FindDeep(names).CastObject<T>();

        public static T FindDeep<T>(this Component holder, params string[] names) where T : Object => FindDeep<T>(holder.transform, names);

        public static T FindByPath<T>(this Transform source, string relativePath) where T : Object
        {
            var names = relativePath.Split('/');
            return source.FindDeep<T>(names);
        }
        
        public static Object GetObject(this Component root, Type type)
        {
            if (type == typeof(Transform))
                return root.transform;

            if (type == typeof(GameObject))
                return root.gameObject;

            return typeof(Component).IsAssignableFrom(type) ? root.GetComponent(type) : null;
        }

        public static Object FindByPath(this Transform source, string relativePath, Type type)
        {
            var names = relativePath.Split('/');
            var result = source.FindDeep(names);
            if (result == null)
                return null;

            if (type == typeof(Transform))
                return result;

            if (type == typeof(GameObject))
                return result.gameObject;

            return typeof(Component).IsAssignableFrom(type) ? result.GetComponent(type) : null;
        }

        public static T CastObject<T>(this Component source) where T : Object
        {
            var type = typeof(T);
            if(type == typeof(Transform))
                return source.transform as T;
            if(type == typeof(GameObject))
                return source.gameObject as T;
            if(typeof(Component).IsAssignableFrom(type))
                return source.GetComponent<T>();
            return source as T;
        }

        [Flags]
        public enum AnchorConfig
        {
            NONE,
            LEFT = 1 << 0,
            RIGHT = 1 << 1,
            BOTTOM = 1 << 2,
            TOP = 1 << 3,
            ALL = LEFT | RIGHT | BOTTOM | TOP
        }

        public struct MarginConfig
        {
            public AnchorConfig anchorConfig;
            public float? left;
            public float? right;
            public float? bottom;
            public float? top;

            /// <param name="anchorConfig"></param>
            /// <param name="left">offsetMin.x</param>
            /// <param name="right">offsetMax.x</param>
            /// <param name="bottom">offsetMin.y</param>
            /// <param name="top">offsetMax.y</param>
            public MarginConfig(AnchorConfig anchorConfig, float? left, float? right, float? bottom, float? top)
            {
                this.anchorConfig = anchorConfig;
                this.left = left;
                this.right = right;
                this.bottom = bottom;
                this.top = top;
            }

            public static implicit operator MarginConfig((float? left, float? right, float? bottom, float? top) config)
            {
                return new MarginConfig
                {
                    left = config.left,
                    right = config.right,
                    bottom = config.bottom,
                    top = config.top,
                    anchorConfig = config switch
                    {
                        { left: not null, right: not null, bottom: not null, top: not null } => AnchorConfig.ALL,
                        { left: not null, right: not null, bottom: not null } => AnchorConfig.LEFT | AnchorConfig.RIGHT | AnchorConfig.BOTTOM,
                        { left: not null, right: not null, top: not null } => AnchorConfig.LEFT | AnchorConfig.RIGHT | AnchorConfig.TOP,
                        { bottom: not null, top: not null, left: not null } => AnchorConfig.BOTTOM | AnchorConfig.TOP | AnchorConfig.LEFT,
                        { bottom: not null, top: not null, right: not null } => AnchorConfig.BOTTOM | AnchorConfig.TOP | AnchorConfig.RIGHT,
                        { left: not null, right: not null } => AnchorConfig.LEFT | AnchorConfig.RIGHT,
                        { bottom: not null, top: not null } => AnchorConfig.BOTTOM | AnchorConfig.TOP,
                        { left: not null, bottom: not null } => AnchorConfig.LEFT | AnchorConfig.BOTTOM,
                        { left: not null, top: not null } => AnchorConfig.LEFT | AnchorConfig.TOP,
                        { right: not null, bottom: not null } => AnchorConfig.RIGHT | AnchorConfig.BOTTOM,
                        { right: not null, top: not null } => AnchorConfig.RIGHT | AnchorConfig.TOP,
                        { left: not null } => AnchorConfig.LEFT,
                        { right: not null } => AnchorConfig.RIGHT,
                        { bottom: not null } => AnchorConfig.BOTTOM,
                        { top: not null } => AnchorConfig.TOP,
                        _ => AnchorConfig.NONE
                    }
                };
            }
        }

        public static void Margin(this RectTransform rectTransform, MarginConfig config)
        {
            var anchorMin = rectTransform.anchorMin;
            var anchorMax = rectTransform.anchorMax;
            var offsetMin = rectTransform.offsetMin;
            var offsetMax = rectTransform.offsetMax;
            var anchoredPosition = rectTransform.anchoredPosition;

            switch (config.anchorConfig)
            {
                case AnchorConfig.LEFT:
                    left();
                    break;
                case AnchorConfig.RIGHT:
                    right();
                    break;
                case AnchorConfig.BOTTOM:
                    bottom();
                    break;
                case AnchorConfig.TOP:
                    top();
                    break;
                case AnchorConfig.LEFT | AnchorConfig.BOTTOM:
                    left();
                    bottom();
                    break;
                case AnchorConfig.LEFT | AnchorConfig.TOP:
                    left();
                    top();
                    break;
                case AnchorConfig.RIGHT | AnchorConfig.BOTTOM:
                    right();
                    bottom();
                    break;
                case AnchorConfig.RIGHT | AnchorConfig.TOP:
                    right();
                    top();
                    break;
                case AnchorConfig.LEFT | AnchorConfig.RIGHT:
                    horizontalBothSide();
                    break;
                case AnchorConfig.BOTTOM | AnchorConfig.TOP:
                    verticalBothSide();
                    break;
                case AnchorConfig.LEFT | AnchorConfig.RIGHT | AnchorConfig.BOTTOM:
                    horizontalBothSide();
                    bottom();
                    break;
                case AnchorConfig.LEFT | AnchorConfig.RIGHT | AnchorConfig.TOP:
                    horizontalBothSide();
                    top();
                    break;
                case AnchorConfig.BOTTOM | AnchorConfig.TOP | AnchorConfig.LEFT:
                    verticalBothSide();
                    left();
                    break;
                case AnchorConfig.BOTTOM | AnchorConfig.TOP | AnchorConfig.RIGHT:
                    verticalBothSide();
                    right();
                    break;
                case AnchorConfig.ALL:
                    horizontalBothSide();
                    verticalBothSide();
                    anchoredPosition.x = 0;
                    anchoredPosition.y = 0;
                    break;
                case AnchorConfig.NONE:
                default:
                    throw new ArgumentOutOfRangeException($"{nameof(config)}", "\'Margin Config\' is wrong format!");
            }

            rectTransform.anchorMin = anchorMin;
            rectTransform.anchorMax = anchorMax;
            rectTransform.offsetMin = offsetMin;
            rectTransform.offsetMax = offsetMax;
            rectTransform.anchoredPosition = anchoredPosition;
            return;

            void left()
            {
                if(!config.left.HasValue)
                    return;
                anchorMin.x = 0;
                anchorMax.x = 0;
                anchoredPosition.x = config.left.Value;
            }

            void right()
            {
                if(!config.right.HasValue)
                    return;
                anchorMin.x = 1;
                anchorMax.x = 1;
                anchoredPosition.x = -config.right.Value;
            }

            void bottom()
            {
                if(!config.bottom.HasValue)
                    return;
                anchorMin.y = 0;
                anchorMax.y = 0;
                anchoredPosition.y = config.bottom.Value;
            }

            void top()
            {
                if(!config.top.HasValue)
                    return;
                anchorMin.y = 1;
                anchorMax.y = 1;
                anchoredPosition.y = -config.top.Value;
            }

            void horizontalBothSide()
            {
                if(!config.left.HasValue || !config.right.HasValue)
                    return;
                anchorMin.x = 0;
                anchorMax.x = 1;
                offsetMin.x = config.left.Value;
                offsetMax.x = -config.right.Value;
            }

            void verticalBothSide()
            {
                if(!config.bottom.HasValue || !config.top.HasValue)
                    return;
                anchorMin.y = 0;
                anchorMax.y = 1;
                offsetMin.y = config.bottom.Value;
                offsetMax.y = -config.top.Value;
            }
        }

        public static Vector2 ConvertWorldToAnchored(this Vector3 position, Camera camera, RectTransform rect)
        {
            var screenPosition = (Vector2)camera.WorldToScreenPoint(position);
            return RectTransformUtility.ScreenPointToLocalPointInRectangle(rect, screenPosition, camera, out var result) 
                ? result : default;
        }
    }
}