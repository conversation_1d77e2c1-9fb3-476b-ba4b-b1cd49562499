using System;
using System.Collections.Generic;

namespace OnePuz.Extensions
{
    public static class QueryExtension
    {
        public static bool Any<T1, T2>(this IList<T1> list, T2 arg, Func<T1, T2, bool> condition)
        {
            for (var i = 0; i < list.Count; i++)
            {
                if (condition(list[i], arg))
                    return true;
            }

            return false;
        }
    }
}