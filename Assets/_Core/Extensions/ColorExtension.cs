using UnityEngine;

namespace OnePuz.Extensions
{
    public static class ColorExtension
    {
        // public static void ToHSV(this Color color, out (float hue, float saturation, float brightness) result)
        //     => Color.RGBToHSV(color, out result.hue, out result.saturation, out result.brightness);

        public static Vector3 ToHSV(this Color color)
        {
            Color.RGBToHSV(color, out var hue, out var saturation, out var brightness);
            return new Vector3(hue, saturation, brightness);
        }

        public static Color ToRGB(this (float hue, float saturation, float brightness) tuple)
            => Color.HSVToRGB(tuple.hue, tuple.saturation, tuple.brightness);
        
        public static Color ToRGB(this Vector3 hsv)
            => Color.HSVToRGB(hsv.x, hsv.y, hsv.z);

        public static Color ParseFromHtml(this string htmlString)
            => ColorUtility.TryParseHtmlString(htmlString, out var result) ? result : Color.clear;
        
        public static string ToHtmlString(this Color color) => ColorUtility.ToHtmlStringRGB(color);

        public static bool Approx(this (float hue, float saturation, float brightness) colorHSV, (float hue, float saturation, float brightness) arg)
            => colorHSV.hue.Approx(arg.hue) && colorHSV.saturation.Approx(arg.saturation) && colorHSV.brightness.Approx(arg.brightness);

        public static bool Approx(this Color src, Color arg, float tolerance = 1E-06f) =>
            src.r.Approx(arg.r, tolerance) &&
            src.g.Approx(arg.g, tolerance) &&
            src.b.Approx(arg.b, tolerance) &&
            src.a.Approx(arg.a, tolerance);
        
        public static bool Approx(this Color32 src, Color32 arg, byte tolerance = 0) =>
            src.r.Approx(arg.r, tolerance) &&
            src.g.Approx(arg.g, tolerance) &&
            src.b.Approx(arg.b, tolerance) &&
            src.a.Approx(arg.a, tolerance);

        public static float AmplitudeBetween(Color32 a, Color32 b, bool includeAlpha = false) 
            => !includeAlpha
                ? Vector3.Distance(new Vector3(a.r, a.g, a.b), new Vector3(b.r, b.g, b.b)) 
                : Vector4.Distance(new Vector4(a.r, a.g, a.b, a.a), new Vector4(b.r, b.g, b.b, b.a));

        public static float AmplitudeBetween(Color a, Color b, bool includeAlpha = false)
        {
            if (!includeAlpha)
                return Vector3.Distance(a.ToHSV(), b.ToHSV());
            
            Color.RGBToHSV(a,out var ha, out var sa, out var ba);
            Color.RGBToHSV(b,out var hb, out var sb, out var bb);
            return Vector4.Distance(new Vector4(ha, sa, ba, a.a), new Vector4(hb, sb, bb, b.a));
        }

        public static float GetInverseAlpha(this float source, float resonanceAlpha)
            => (resonanceAlpha - source) / (1 - source);
        
        
        
        /// <summary>
        /// Convert a hex color string to a Unity Color.
        /// </summary>
        /// <param name="hex">The hex color string (e.g., "#RRGGBB" or "#RRGGBBAA").</param>
        /// <returns>The corresponding Color object.</returns>
        public static Color ToColor(this string hex)
        {
            hex = hex.Replace("#", "");

            if (hex.Length == 6)
            {
                hex += "FF"; // Add alpha if not specified
            }

            if (hex.Length != 8)
            {
                Debug.LogError("Invalid hex color string length. Expected 6 or 8 characters, got " + hex.Length);
                return Color.black; // Return black as a fallback
            }

            byte r = byte.Parse(hex.Substring(0, 2), System.Globalization.NumberStyles.HexNumber);
            byte g = byte.Parse(hex.Substring(2, 2), System.Globalization.NumberStyles.HexNumber);
            byte b = byte.Parse(hex.Substring(4, 2), System.Globalization.NumberStyles.HexNumber);
            byte a = byte.Parse(hex.Substring(6, 2), System.Globalization.NumberStyles.HexNumber);

            return new Color32(r, g, b, a);
        }
    }
}