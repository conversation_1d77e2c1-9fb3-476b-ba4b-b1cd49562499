using Sirenix.OdinInspector.Editor;
using Sirenix.Utilities;
using Sirenix.Utilities.Editor;
using UnityEditor;
using UnityEngine;
using System.Linq;
using OnePuz.DailyReward.Definitions;
using OnePuz.Definition;
using OnePuz.Leaderboard;
using OnePuz.Level;
using OnePuz.Shop.Definition;
using OnePuz.UI;

namespace OnePuz.Ads
{
    public class SettingWindow : OdinMenuEditorWindow
    {
        [MenuItem("Tools/OnePuz/Definitions")]
        private static void Open()
        {
            var window = GetWindow<SettingWindow>("Definitions");
            window.position = GUIHelper.GetEditorWindowRect().AlignCenter(800, 500);
        }

        protected override OdinMenuTree BuildMenuTree()
        {
            var tree = new OdinMenuTree(true)
            {
                DefaultMenuStyle =
                {
                    IconSize = 28.00f
                },
                Config =
                {
                    DrawSearchToolbar = true
                }
            };

            // Find assets by type instead of hardcoded paths
            AddAssetByType<AdDefinition>(tree, "Ads");
            
            // Adds all ads.
            tree.AddAllAssetsAtPath("Ads", "Assets/_Core/Ads/Resources/Ads", typeof(BaseAdUnitDefinition), true, true);
            
            // Find and add other definition assets by type
            AddAssetByType<UIPrefabDefinitions>(tree, "UI Prefabs");
            AddAssetByType<GameDefinition>(tree, "Game Definition");
            AddAssetByType<LeaderboardSetting>(tree, "Leaderboard");
            AddAssetByType<LevelGroupDefinition>(tree, "Level Group Definition");
            AddAssetByType<ShopDefinition>(tree, "Shop Definition");
            AddAssetByType<DailyRewardDefinition>(tree, "Daily Reward Definition");
            AddAssetByType<CurrencyDefinition>(tree, "Currency Definition");
            AddAssetByType<BoosterDefinition>(tree, "Booster Definition");
            AddAssetByType<RewardDefinition>(tree, "Reward Definition");
            AddAssetByType<LevelChestDefinition>(tree, "Level Chest Definition");
            AddAssetByType<TutorialDefinition>(tree, "Tutorial Definition");

            return tree;
        }

        private void AddAssetByType<T>(OdinMenuTree tree, string menuName) where T : ScriptableObject
        {
            // Find all assets of type T
            string[] guids = AssetDatabase.FindAssets($"t:{typeof(T).Name}");
            
            if (guids.Length > 0)
            {
                // Get the first asset found
                string path = AssetDatabase.GUIDToAssetPath(guids[0]);
                tree.AddAssetAtPath(menuName, path);
                
                // Log if multiple assets of the same type are found
                if (guids.Length > 1)
                {
                    Debug.LogWarning($"Multiple {typeof(T).Name} assets found. Using the first one at: {path}");
                }
            }
            else
            {
                Debug.LogWarning($"No {typeof(T).Name} asset found.");
            }
        }
    }
}