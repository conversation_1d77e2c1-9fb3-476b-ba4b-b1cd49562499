using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.ResourceManagement.AsyncOperations;

namespace OnePuz.Addressable
{
    public static class AddressableHelper
    {
        private static readonly Dictionary<string, AsyncOperationHandle> _handles = new ();

        public static async UniTask<T> LoadAsync<T>(AssetReference reference) where T : Object
        {
            if (reference == null)
            {
                OLogger.LogError("Attempted to load null AssetReference");
                return null;
            }

            var key = reference.RuntimeKey.ToString();
            
            if (_handles.TryGetValue(key, out var handle))
            {
                if (handle.IsDone)
                    return (T)handle.Result;
                
                await handle;
                return (T)handle.Result;
            }

            var opHandle = reference.LoadAssetAsync<T>();
            _handles.Add(key, opHandle);
            await opHandle;
            return opHandle.Result;
        }

        public static void Unload(AssetReference reference)
        {
            if (reference == null) return;
            
            var key = reference.RuntimeKey.ToString();
            if (!_handles.TryGetValue(key, out var handle)) return;
            Addressables.Release(handle);
            _handles.Remove(key);
        }

        public static void UnloadAll()
        {
            foreach (var handle in _handles.Values)
            {
                if (handle.IsValid())
                {
                    Addressables.Release(handle);
                }
            }
            _handles.Clear();
        }
    }
}