using System;
using _FeatureHub.Utilities;
using OnePuz.Extensions;
using PrimeTween;

namespace OnePuz.Util
{
    public class ArithmeticCounter
    {
        private int m_ValueDisplayed;
        
        private int m_ValueTargeted;
        
        private Tween m_Tween;

        private readonly CallbackWrapper<int> m_ValueChangedListener = new();
        
        private readonly CallbackWrapper m_CompletedListener = new();

        public ArithmeticCounter(int defaultValue)
        {
            m_ValueDisplayed = defaultValue;
            m_ValueTargeted = defaultValue;
        }

        ~ArithmeticCounter()
        {
            m_Tween.Stop();
        }

        public void ResetTo(int value)
        {
            m_ValueDisplayed = value;
            m_ValueTargeted = value;
        }

        public void SetTarget(int targetValue) => m_ValueTargeted = targetValue;
        
        public ArithmeticCounter Play(int targetValue, float durationMin = 0.5f, float durationMax = 1.0f, float delay = 0.0f)
        {
            m_ValueTargeted = targetValue;
            return Play(durationMin, durationMax, delay);
        }

        public ArithmeticCounter Play(float durationMin = 0.5f, float durationMax = 1.0f, float delay = 0.0f)
        {
            if (m_ValueDisplayed == m_ValueTargeted)
                return this;
            
            m_Tween.Stop();
            var duration = ((m_ValueTargeted - m_ValueDisplayed) / 120f).Claim(durationMin, durationMax);
            m_Tween = Tween.Custom(this, m_ValueDisplayed, m_ValueTargeted, duration,
                    (target, value) => target.CountIterator(value), Ease.Linear, startDelay: delay)
                .OnComplete(this, target => target.HandleCompleted());
            return this;
        }
        
        public ArithmeticCounter OnValueChanged<T>(T target, Action<T, int> callback) where T : class
        {
            m_ValueChangedListener.Add(target, callback);
            return this;
        }

        public void OnComplete<T>(T target, Action<T> complete) where T : class => m_CompletedListener.Add(target, complete);

        private void CountIterator(float value)
        {
            m_ValueDisplayed = (int)value;
            m_ValueChangedListener.Execute(m_ValueDisplayed);
        }

        private void HandleCompleted()
        {
            m_ValueDisplayed = m_ValueTargeted;
            m_ValueChangedListener.Execute(m_ValueTargeted);
            m_CompletedListener.Execute().Release();
        }
    }
}