// SPDX-License-Identifier: MIT
// Copyright (c) 2016-2021 <PERSON> (@JuDelCo)

using System;
using OnePuz.FSM;
using OnePuz.Handlers;
using OnePuz.Services;

namespace OnePuz.TimeHandler
{
	public partial class FrameTimer<T> : IFrameTimer where T : ITimeEvent
	{
		public FrameTimer(int frames, Action onCompleted, IService service) : this(frames, onCompleted)
		{
			var linkHandler = new ObjectLinkHandler<IService>(service);
			this.updateCondition = () => linkHandler.IsActive;
		}

		public FrameTimer(int frames, Action onCompleted, State state) : this(frames, onCompleted)
		{
			var linkHandler = new StateLinkHandler(state);
			this.updateCondition = () => linkHandler.IsActive;
		}
	}
}
