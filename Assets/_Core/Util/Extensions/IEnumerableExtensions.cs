// SPDX-License-Identifier: MIT
// Copyright (c) 2016-2021 <PERSON> (@JuDelCo)

using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

namespace OnePuz.Extensions
{
    public static class IEnumerableExtensions
    {
        public static IEnumerable<TSource> Concatenate<TSource>(this IEnumerable<TSource> self, params IEnumerable<TSource>[] others)
        {
            using (var enumerator = self.GetEnumerator())
            {
                while (enumerator.MoveNext())
                {
                    yield return enumerator.Current;
                }
            }

            for (int i = 0, othersCount = others.Length; i < othersCount; ++i)
            {
                var ienumerable = others[i];

                using (var enumerator = ienumerable.GetEnumerator())
                {
                    while (enumerator.MoveNext())
                    {
                        yield return enumerator.Current;
                    }
                }
            }
        }

        public static TSource Find<TSource>(this IEnumerable<TSource> self, TSource defaultValue, Func<TSource, bool> predicate)
        {
            var result = defaultValue;

            using (var enumerator = self.GetEnumerator())
            {
                while (enumerator.MoveNext())
                {
                    if (predicate(enumerator.Current))
                    {
                        result = enumerator.Current;
                        break;
                    }
                }
            }

            return result;
        }

        public static TSource FindUnique<TSource>(this IEnumerable<TSource> self, TSource defaultValue, Func<TSource, bool> predicate)
        {
            var result = defaultValue;
            var found = false;

            using (var enumerator = self.GetEnumerator())
            {
                while (enumerator.MoveNext())
                {
                    if (predicate(enumerator.Current))
                    {
                        if (found)
                        {
                            throw new InvalidOperationException("Found two elements that match the predicate");
                        }

                        result = enumerator.Current;
                        found = true;
                    }
                }
            }

            return result;
        }

        public static IEnumerable<TResult> Flatten<TSource, TResult>(this IEnumerable<TSource> self, Func<TSource, IEnumerable<TResult>> selector)
        {
            using (var enumerator = self.GetEnumerator())
            {
                while (enumerator.MoveNext())
                {
                    using (var internalEnumerator = selector(enumerator.Current).GetEnumerator())
                    {
                        while (internalEnumerator.MoveNext())
                        {
                            yield return internalEnumerator.Current;
                        }
                    }
                }
            }
        }

        public static void ForEach<TSource>(this IEnumerable<TSource> self, Action<TSource> action)
        {
            using (var enumerator = self.GetEnumerator())
            {
                while (enumerator.MoveNext())
                {
                    action(enumerator.Current);
                }
            }
        }

        public static void ForEach<TSource>(this IEnumerable<TSource> self, Func<TSource, bool> action)
        {
            using (var enumerator = self.GetEnumerator())
            {
                while (enumerator.MoveNext())
                {
                    if (!action(enumerator.Current))
                    {
                        break;
                    }
                }
            }
        }

        //
        public static void ForEachReverse<TSource>(this IEnumerable<TSource> self, Action<TSource> action)
        {
            var reversed = self.Reverse();

            using var enumerator = reversed.GetEnumerator();
            while (enumerator.MoveNext())
            {
                action(enumerator.Current);
            }
        }

        public static void ForEachReverse<TSource>(this IEnumerable<TSource> self, Func<TSource, bool> action)
        {
            var reversed = self.Reverse();

            using var enumerator = reversed.GetEnumerator();
            while (enumerator.MoveNext())
            {
                if (!action(enumerator.Current))
                {
                    break;
                }
            }
        }

        public static IEnumerable<TResult> Map<TSource, TResult>(this IEnumerable<TSource> self, Func<TSource, TResult> selector)
        {
            using (var enumerator = self.GetEnumerator())
            {
                while (enumerator.MoveNext())
                {
                    yield return selector(enumerator.Current);
                }
            }
        }
    }
}