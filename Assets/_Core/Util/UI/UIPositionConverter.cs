using UnityEngine;

namespace OnePuz.Utilities
{
    /// <summary>
    /// Utility class for converting positions between UI space and world space.
    /// </summary>
    public static class UIPositionConverter
    {
        /// <summary>
        /// Gets the world position of a UI element's center.
        /// </summary>
        /// <param name="rectTransform">The RectTransform of the UI element</param>
        /// <returns>The center position in world space</returns>
        public static Vector3 GetUIElementWorldCenter(RectTransform rectTransform)
        {
            if (rectTransform == null)
                return Vector3.zero;
                
            Vector3[] corners = new Vector3[4];
            rectTransform.GetWorldCorners(corners);
            return (corners[0] + corners[2]) * 0.5f;
        }
        
        /// <summary>
        /// Converts a UI element's position to world space position at a specific distance from camera.
        /// </summary>
        /// <param name="uiElement">The UI element's transform</param>
        /// <param name="camera">Camera to use for conversion</param>
        /// <param name="targetDistance">Distance from camera for the resulting world position</param>
        /// <returns>The position in world space</returns>
        public static Vector3 UIToWorldPosition(Transform uiElement, Camera camera, float targetDistance)
        {
            if (uiElement == null || camera == null)
                return Vector3.zero;
                
            RectTransform rectTransform = uiElement.GetComponent<RectTransform>();
            if (rectTransform == null)
                return uiElement.position;
                
            Vector3 centerWorld = GetUIElementWorldCenter(rectTransform);
            Canvas canvas = uiElement.GetComponentInParent<Canvas>();
            
            if (canvas == null)
                return centerWorld;
                
            switch (canvas.renderMode)
            {
                case RenderMode.ScreenSpaceOverlay:
                    Vector3 screenPos = RectTransformUtility.WorldToScreenPoint(camera, centerWorld);
                    return camera.ScreenToWorldPoint(new Vector3(screenPos.x, screenPos.y, targetDistance));
                    
                case RenderMode.ScreenSpaceCamera:
                    Camera canvasCamera = canvas.worldCamera ? canvas.worldCamera : camera;
                    Vector3 screenPosCamera = canvasCamera.WorldToScreenPoint(centerWorld);
                    return canvasCamera.ScreenToWorldPoint(new Vector3(screenPosCamera.x, screenPosCamera.y, targetDistance));
                    
                case RenderMode.WorldSpace:
                default:
                    return centerWorld;
            }
        }
        
        /// <summary>
        /// Converts a world position to UI space for a specific canvas.
        /// </summary>
        /// <param name="worldPosition">The world position to convert</param>
        /// <param name="canvas">Target canvas</param>
        /// <param name="camera">Camera to use for conversion</param>
        /// <returns>Position in UI space</returns>
        public static Vector2 WorldToUIPosition(Vector3 worldPosition, Canvas canvas, Camera camera)
        {
            if (canvas == null || camera == null)
                return Vector2.zero;
                
            RectTransform canvasRect = canvas.GetComponent<RectTransform>();
            if (canvasRect == null)
                return Vector2.zero;
                
            Vector2 screenPoint = RectTransformUtility.WorldToScreenPoint(camera, worldPosition);
            RectTransformUtility.ScreenPointToLocalPointInRectangle(canvasRect, screenPoint, 
                canvas.renderMode == RenderMode.ScreenSpaceOverlay ? null : camera, out Vector2 localPoint);
                
            return localPoint;
        }
        
        /// <summary>
        /// Gets the appropriate camera for a canvas.
        /// </summary>
        /// <param name="canvas">The canvas</param>
        /// <param name="fallbackCamera">Fallback camera if canvas camera is not set</param>
        /// <returns>The appropriate camera for the canvas</returns>
        public static Camera GetCanvasCamera(Canvas canvas, Camera fallbackCamera)
        {
            if (canvas == null)
                return fallbackCamera;
                
            return canvas.renderMode == RenderMode.ScreenSpaceCamera && canvas.worldCamera != null 
                ? canvas.worldCamera 
                : fallbackCamera;
        }
    }
}