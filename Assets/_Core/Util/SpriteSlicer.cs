using UnityEngine;
using UnityEditor;
using System.IO;
using System.Linq;

namespace OnePuz.Utilities
{
#if UNITY_EDITOR
    public class SpriteSlicer : EditorWindow
    {
        private Texture2D _spriteSheet;
        private string _outputFolder = "Assets/SlicedSprites";

        [MenuItem("Tools/OnePuz/Sprite Slicer")]
        public static void ShowWindow()
        {
            GetWindow<SpriteSlicer>("Sprite Slicer");
        }

        private void OnGUI()
        {
            GUILayout.Label("Sprite Slicer", EditorStyles.boldLabel);
            _spriteSheet = (Texture2D)EditorGUILayout.ObjectField("Sprite Sheet", _spriteSheet, typeof(Texture2D), false);
            _outputFolder = EditorGUILayout.TextField("Output Folder", _outputFolder);

            if (!GUILayout.Button("Slice")) return;
            if (_spriteSheet)
            {
                SliceSpriteSheet();
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "Please select a sprite sheet.", "OK");
            }
        }

        private void SliceSpriteSheet()
        {
            var path = AssetDatabase.GetAssetPath(_spriteSheet);
            var textureImporter = AssetImporter.GetAtPath(path) as TextureImporter;

            if (textureImporter != null && textureImporter.spriteImportMode == SpriteImportMode.Multiple)
            {
                var sprites = AssetDatabase.LoadAllAssetsAtPath(path).OfType<Sprite>().ToArray();
                foreach (var sprite in sprites)
                {
                    SaveSpriteAsPNG(sprite);
                }

                AssetDatabase.Refresh();
                EditorUtility.DisplayDialog("Success", "Slicing completed!", "OK");
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "The selected texture is not set to Sprite Mode Multiple.", "OK");
            }
        }

        private void SaveSpriteAsPNG(Sprite sprite)
        {
            var rect = sprite.rect;
            var croppedTexture = new Texture2D((int)rect.width, (int)rect.height);
            var pixels = sprite.texture.GetPixels((int)rect.x, (int)rect.y, (int)rect.width, (int)rect.height);
            croppedTexture.SetPixels(pixels);
            croppedTexture.Apply();

            var bytes = croppedTexture.EncodeToPNG();
            var filePath = Path.Combine(_outputFolder, sprite.name + ".png");
            File.WriteAllBytes(filePath, bytes);
        }
    }
#endif
}