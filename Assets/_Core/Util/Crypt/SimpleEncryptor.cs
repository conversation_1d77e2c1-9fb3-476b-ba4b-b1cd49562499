using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace OnePuz.Crypt
{
    public class SimpleEncryptor
    {
        private static readonly byte[] Salt = Encoding.ASCII.GetBytes("OnePuz_salt");

        public static string Encrypt(string plainText, string password)
        {
            using (var aes = new RijndaelManaged())
            {
                var key = new Rfc2898DeriveBytes(password, Salt);
                aes.Key = key.GetBytes(aes.KeySize / 8);
                aes.IV = key.GetBytes(aes.BlockSize / 8);

                using (var encryptor = aes.CreateEncryptor(aes.Key, aes.IV))
                using (var ms = new MemoryStream())
                {
                    using (var cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
                    using (var sw = new StreamWriter(cs))
                    {
                        sw.Write(plainText);
                    }
                    return Convert.ToBase64String(ms.ToArray());
                }
            }
        }

        public static string Decrypt(string cipherText, string password)
        {
            using (var aes = new RijndaelManaged())
            {
                var key = new Rfc2898DeriveBytes(password, Salt);
                aes.Key = key.GetBytes(aes.KeySize / 8);
                aes.IV = key.GetBytes(aes.BlockSize / 8);

                using (var decryptor = aes.CreateDecryptor(aes.Key, aes.IV))
                using (var ms = new MemoryStream(Convert.FromBase64String(cipherText)))
                using (var cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read))
                using (var sr = new StreamReader(cs))
                {
                    return sr.ReadToEnd();
                }
            }
        }
    }
}