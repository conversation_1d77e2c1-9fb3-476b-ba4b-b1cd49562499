#if USING_IAP
#if UNITY_ANDROID || UNITY_IPHONE || UNITY_STANDALONE_OSX || UNITY_TVOS
// You must obfuscate your secrets using Window > Unity IAP > Receipt Validation Obfuscator
// before receipt validation will compile in this sample.
// #define RECEIPT_VALIDATION
#endif

//#define DELAY_CONFIRMATION // Returns PurchaseProcessingResult.Pending from ProcessPurchase, then calls ConfirmPendingPurchase after a delay
//#define USE_PAYOUTS // Enables use of PayoutDefinitions to specify what the player should receive when a product is purchased
//#define INTERCEPT_PROMOTIONAL_PURCHASES // Enables intercepting promotional purchases that come directly from the Apple App Store
// #define SUBSCRIPTION_MANAGER //Enables subscription product manager for AppleStore and GooglePlay store
//#define AGGRESSIVE_INTERRUPT_RECOVERY_GOOGLEPLAY // Enables also using getPurchaseHistory to recover from purchase interruptions, assuming developer is deduplicating to protect against "duplicate on cancel" flow

using OnePuz.Services;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using AppsFlyerSDK;
using Cysharp.Threading.Tasks;
using Newtonsoft.Json;
using OnePuz.Ads;
using UnityEngine;
using UnityEngine.Purchasing;
using UnityEngine.Purchasing.Extension;

#if RECEIPT_VALIDATION
using UnityEngine.Purchasing.Security;
#endif

namespace OnePuz.Purchase
{
    public struct PurchaseInitializedEvent
    {
    }

    public struct OnPurchasedEvent
    {
        public OnPurchasedEvent(bool success, string productId)
        {
            this.success = success;
            this.productId = productId;
        }

        public bool success;
        public string productId;
    }

    [System.Serializable]
    public class ProductCatalog
    {
        public string productId;
        public ProductType productType;
    }

    public class PurchaseService : MonoBehaviour, IServiceLoad, IDetailedStoreListener
    {
        public delegate void OnPurchased(bool succeed, string productId);

        public OnPurchased onPurchased;

        // Unity IAP objects
        private IStoreController _controller;

        private IAppleExtensions _appleExtensions;

        // private ISamsungAppsExtensions m_SamsungExtensions;
        private IMicrosoftExtensions _microsoftExtensions;
        private ITransactionHistoryExtensions _transactionHistoryExtensions;
        private IGooglePlayStoreExtensions _googlePlayStoreExtensions;

#pragma warning disable 0414
        private bool _isGooglePlayStoreSelected;
#pragma warning restore 0414
        // private bool m_IsSamsungAppsStoreSelected;

        private bool _purchaseInProgress;

#if RECEIPT_VALIDATION
        private CrossPlatformValidator _validator;
#endif

        private List<ProductCatalog> _productCatalogs = new List<ProductCatalog>();

        public bool HasInitialized { get; private set; } = false;

        public void Load()
        {
            var module = StandardPurchasingModule.Instance();

            // The FakeStore supports: no-ui (always succeeding), basic ui (purchase pass/fail), and
            // developer ui (initialization, purchase, failure code setting). These correspond to
            // the FakeStoreUIMode Enum values passed into StandardPurchasingModule.useFakeStoreUIMode.
            module.useFakeStoreUIMode = FakeStoreUIMode.StandardUser;

            var builder = ConfigurationBuilder.Instance(module);

            // Set this to true to enable the Microsoft IAP simulator for local testing.
            builder.Configure<IMicrosoftConfiguration>().useMockBillingSystem = false;

            _isGooglePlayStoreSelected =
                Application.platform == RuntimePlatform.Android && module.appStore == AppStore.GooglePlay;

#if AGGRESSIVE_INTERRUPT_RECOVERY_GOOGLEPLAY
        // For GooglePlay, if we have access to a backend server to deduplicate purchases, query purchase history
        // when attempting to recover from a network-interruption encountered during purchasing. Strongly recommend
        // deduplicating transactions across app re-installations because this relies upon the on-device, deletable
        // TransactionLog database.
        builder.Configure<IGooglePlayConfiguration>().aggressivelyRecoverLostPurchases = true;
        // Use purchaseToken instead of orderId for all transactions to avoid non-unique transactionIDs for a
        // single purchase; two ProcessPurchase calls for one purchase, differing only by which field of the receipt
        // is used for the Product.transactionID. Automatically true if aggressivelyRecoverLostPurchases is enabled
        // and this API is not called at all.
        builder.Configure<IGooglePlayConfiguration>().UsePurchaseTokenForTransactionId(true);
#endif

            // Define our products.
            // Either use the Unity IAP Catalog, or manually use the ConfigurationBuilder.AddProduct API.
            // Use IDs from both the Unity IAP Catalog and hardcoded IDs via the ConfigurationBuilder.AddProduct API.

            // Use the products defined in the IAP Catalog GUI.
            // E.g. Menu: "Window" > "Unity IAP" > "IAP Catalog", then add products, then click "App Store Export".
            var catalog = UnityEngine.Purchasing.ProductCatalog.LoadDefaultCatalog();

            foreach (var product in catalog.allValidProducts)
            {
                if (product.allStoreIDs.Count > 0)
                {
                    var ids = new IDs();
                    foreach (var storeID in product.allStoreIDs)
                    {
                        ids.Add(storeID.id, storeID.store);
                    }

                    builder.AddProduct(product.id, product.type, ids);
                }
                else
                {
                    builder.AddProduct(product.id, product.type);
                }
            }

            // In this case our products have the same identifier across all the App stores,
            // except on the Mac App store where product IDs cannot be reused across both Mac and
            // iOS stores.
            // So on the Mac App store our products have different identifiers,
            // and we tell Unity IAP this by using the IDs class.

            // Write Amazon's JSON description of our products to storage when using Amazon's local sandbox.
            // This should be removed from a production build.
            //builder.Configure<IAmazonConfiguration>().WriteSandboxJSON(builder.products);

            // This enables simulated purchase success for Samsung IAP.
            // You would remove this, or set to SamsungAppsMode.Production, before building your release package.
            // builder.Configure<ISamsungAppsConfiguration>().SetMode(SamsungAppsMode.AlwaysSucceed);
            // This records whether we are using Samsung IAP. Currently, ISamsungAppsExtensions.RestoreTransactions
            // displays a blocking Android Activity, so:
            // A) Unity IAP does not automatically restore purchases on Samsung Galaxy Apps
            // B) IAPDemo (this) displays the "Restore" GUI button for Samsung Galaxy Apps
            // m_IsSamsungAppsStoreSelected =
            //     Application.platform == RuntimePlatform.Android && module.appStore == AppStore.SamsungApps;

#if INTERCEPT_PROMOTIONAL_PURCHASES
        // On iOS and tvOS we can intercept promotional purchases that come directly from the App Store.
        // On other platforms this will have no effect; OnPromotionalPurchase will never be called.
        builder.Configure<IAppleConfiguration>().SetApplePromotionalPurchaseInterceptorCallback(OnPromotionalPurchase);
        OLogger.Log("Setting Apple promotional purchase interceptor callback");
#endif

#if RECEIPT_VALIDATION
            var appIdentifier = Application.identifier;
            try
            {
                _validator = new CrossPlatformValidator(GooglePlayTangle.Data(), AppleTangle.Data(), appIdentifier);
            }
            catch (NotImplementedException exception)
            {
                OLogger.Log("Cross Platform Validator Not Implemented: " + exception);
            }
#endif

            // Now we're ready to initialize Unity IAP.
            UnityPurchasing.Initialize(this, builder);
        }

        /// <summary>
        /// Purchasing initialized successfully.
        ///
        /// The <c>IStoreController</c> and <c>IExtensionProvider</c> are
        /// available for accessing purchasing functionality.
        /// </summary>
        /// <param name="controller"> The <c>IStoreController</c> created during initialization. </param>
        /// <param name="extensions"> The <c>IExtensionProvider</c> created during initialization. </param>
        public void OnInitialized(IStoreController controller, IExtensionProvider extensions)
        {
            _controller = controller;
            _appleExtensions = extensions.GetExtension<IAppleExtensions>();
            _microsoftExtensions = extensions.GetExtension<IMicrosoftExtensions>();
            _transactionHistoryExtensions = extensions.GetExtension<ITransactionHistoryExtensions>();
            _googlePlayStoreExtensions = extensions.GetExtension<IGooglePlayStoreExtensions>();

            // Sample code for manually finish a transaction (consume a product on GooglePlay store)
            //m_GooglePlayStoreExtensions.FinishAdditionalTransaction(productId, transactionId);

            // On Apple platforms we need to handle deferred purchases caused by Apple's Ask to Buy feature.
            // On non-Apple platforms this will have no effect; OnDeferred will never be called.
            _appleExtensions.RegisterPurchaseDeferredListener(OnDeferred);

#if SUBSCRIPTION_MANAGER
            Dictionary<string, string> introductoryInfoDict = _appleExtensions.GetIntroductoryPriceDictionary();
#endif
            // Sample code for expose product sku details for apple store
            //Dictionary<string, string> product_details = m_AppleExtensions.GetProductDetails();

            OLogger.LogNotice("Available items:");
            foreach (var item in controller.products.all)
            {
                if (item.availableToPurchase)
                {
                    OLogger.Log(string.Join(" - ",
                        new[]
                        {
                            item.metadata.localizedTitle,
                            item.metadata.localizedDescription,
                            item.metadata.isoCurrencyCode,
                            item.metadata.localizedPrice.ToString(),
                            item.metadata.localizedPriceString,
                            item.transactionID,
                            item.receipt
                        }));
#if INTERCEPT_PROMOTIONAL_PURCHASES
                // Set all these products to be visible in the user's App Store according to Apple's Promotional IAP feature
                // https://developer.apple.com/library/content/documentation/NetworkingInternet/Conceptual/StoreKitGuide/PromotingIn-AppPurchases/PromotingIn-AppPurchases.html
                m_AppleExtensions.SetStorePromotionVisibility(item, AppleStorePromotionVisibility.Show);
#endif

#if SUBSCRIPTION_MANAGER
                    // this is the usage of SubscriptionManager class
                    if (item.receipt != null)
                    {
                        if (item.definition.type == ProductType.Subscription)
                        {
                            string intro_json =
                                (introductoryInfoDict == null ||
                                 !introductoryInfoDict.ContainsKey(item.definition.storeSpecificId))
                                    ? null
                                    : introductoryInfoDict[item.definition.storeSpecificId];
                            SubscriptionManager p = new SubscriptionManager(item, intro_json);
                            SubscriptionInfo info = p.getSubscriptionInfo();
                            var builder = new System.Text.StringBuilder();
                            builder.AppendLine($"product id is: {info.getProductId()}");
                            builder.AppendLine($"purchase date is: {info.getPurchaseDate()}");
                            builder.AppendLine($"subscription next billing date is: {info.getExpireDate()}");
                            builder.AppendLine($"is subscribed? {info.isSubscribed().ToString()}");
                            builder.AppendLine($"is expired? {info.isExpired().ToString()}");
                            builder.AppendLine($"is cancelled? {info.isCancelled()}");
                            builder.AppendLine($"product is in free trial period? {info.isFreeTrial()}");
                            builder.AppendLine($"product is auto renewing? {info.isAutoRenewing()}");
                            builder.AppendLine(
                                $"subscription remaining valid time until next billing date is: {info.getRemainingTime()}");
                            builder.AppendLine(
                                $"is this product in introductory price period? {info.isIntroductoryPricePeriod()}");
                            builder.AppendLine(
                                $"the product introductory localized price is: {info.getIntroductoryPrice()}");
                            builder.AppendLine(
                                $"the product introductory price period is: {info.getIntroductoryPricePeriod()}");
                            builder.AppendLine(
                                $"the number of product introductory price period cycles is: {info.getIntroductoryPricePeriodCycles()}");
                            OLogger.Log(builder.ToString());

                            if (info.isSubscribed() != Result.True) continue;

                            UnlockContentsAsync(info.getProductId()).Forget();
                        }
                        else
                        {
                            OLogger.Log("the product is not a subscription product");
                        }

                        OLogger.LogNotice(
                            $"CheckIfProductIsAvailableForSubscriptionManager {CheckIfProductIsAvailableForSubscriptionManager(item.receipt)}");
                    }
                    else
                    {
                        OLogger.Log("the product should have a valid receipt");
                    }
#endif
                }
            }

            LogProductDefinitions();

            HasInitialized = true;
            Core.Event.Fire(new PurchaseInitializedEvent());
        }

        public void OnInitializeFailed(InitializationFailureReason error, string message)
        {
            HasInitialized = false;
        }

#if SUBSCRIPTION_MANAGER
        private bool CheckIfProductIsAvailableForSubscriptionManager(string receipt)
        {
            var receipt_wrapper = (Dictionary<string, object>)MiniJson.JsonDecode(receipt);
            if (!receipt_wrapper.ContainsKey("Store") || !receipt_wrapper.ContainsKey("Payload"))
            {
                OLogger.Log("The product receipt does not contain enough information");
                return false;
            }

            var store = (string)receipt_wrapper["Store"];
            var payload = (string)receipt_wrapper["Payload"];

            if (payload != null)
            {
                switch (store)
                {
                    case GooglePlay.Name:
                    {
                        var payload_wrapper = (Dictionary<string, object>)MiniJson.JsonDecode(payload);
                        if (!payload_wrapper.ContainsKey("json"))
                        {
                            OLogger.Log(
                                "The product receipt does not contain enough information, the 'json' field is missing");
                            return false;
                        }

                        var original_json_payload_wrapper =
                            (Dictionary<string, object>)MiniJson.JsonDecode((string)payload_wrapper["json"]);
                        if (original_json_payload_wrapper == null ||
                            !original_json_payload_wrapper.ContainsKey("developerPayload"))
                        {
                            OLogger.Log(
                                "The product receipt does not contain enough information, the 'developerPayload' field is missing");
                            return false;
                        }

                        var developerPayloadJSON = (string)original_json_payload_wrapper["developerPayload"];
                        var developerPayload_wrapper =
                            (Dictionary<string, object>)MiniJson.JsonDecode(developerPayloadJSON);
                        if (developerPayload_wrapper == null ||
                            !developerPayload_wrapper.ContainsKey("is_free_trial") ||
                            !developerPayload_wrapper.ContainsKey("has_introductory_price_trial"))
                        {
                            OLogger.Log(
                                "The product receipt does not contain enough information, the product is not purchased using 1.19 or later");
                            return false;
                        }

                        return true;
                    }
                    case AppleAppStore.Name:
                    case AmazonApps.Name:
                    case MacAppStore.Name:
                    {
                        return true;
                    }
                    default:
                    {
                        return false;
                    }
                }
            }

            return false;
        }
#endif

        /// <summary>
        /// A purchase succeeded.
        /// </summary>
        /// <param name="e"> The <c>PurchaseEventArgs</c> for the purchase event. </param>
        /// <returns> The result of the successful purchase </returns>
        public PurchaseProcessingResult ProcessPurchase(PurchaseEventArgs e)
        {
            OLogger.Log("Purchase OK: " + e.purchasedProduct.definition.id);
            OLogger.Log("Receipt: " + e.purchasedProduct.receipt);

            _purchaseInProgress = false;

#if RECEIPT_VALIDATION // Local validation is available for GooglePlay, and Apple stores
            if (_isGooglePlayStoreSelected ||
                Application.platform == RuntimePlatform.IPhonePlayer ||
                Application.platform == RuntimePlatform.OSXPlayer ||
                Application.platform == RuntimePlatform.tvOS)
            {
                try
                {
                    var result = _validator.Validate(e.purchasedProduct.receipt);
                    OLogger.Log("Receipt is valid. Contents:");
                    var builder = new System.Text.StringBuilder();
                    foreach (var productReceipt in result)
                    {
                        builder.Clear();
                        builder.AppendFormat("productID: {0}\n", productReceipt.productID);
                        builder.AppendFormat("purchaseDate: {0}\n", productReceipt.purchaseDate.ToString());
                        builder.AppendFormat("transactionID: {0}\n", productReceipt.transactionID);

                        switch (productReceipt)
                        {
                            case GooglePlayReceipt google:
                                builder.AppendFormat("purchaseState: {0}\n", google.purchaseState);
                                builder.AppendFormat("purchaseToken: {0}\n", google.purchaseToken);
                                break;
                            case AppleInAppPurchaseReceipt apple:
                                builder.AppendFormat("originalTransactionIdentifier: {0}\n",
                                    apple.originalTransactionIdentifier);
                                builder.AppendFormat("subscriptionExpirationDate: {0}\n",
                                    apple.subscriptionExpirationDate.ToString());
                                builder.AppendFormat("cancellationDate: {0}\n", apple.cancellationDate.ToString());
                                builder.AppendFormat("quantity: {0}\n", apple.quantity);
                                break;
                        }

                        OLogger.Log(builder.ToString());

                        // For improved security, consider comparing the signed
                        // IPurchaseReceipt.productId, IPurchaseReceipt.transactionID, and other data
                        // embedded in the signed receipt objects to the data which the game is using
                        // to make this purchase.
                    }
                }
                catch (IAPSecurityException ex)
                {
                    OLogger.Log("Invalid receipt, not unlocking content. " + ex);
                    onPurchased?.Invoke(false, e.purchasedProduct.definition.id);
                    Core.Event.Fire(new OnPurchasedEvent(false, e.purchasedProduct.definition.id));

                    return PurchaseProcessingResult.Complete;
                }
                catch (NotImplementedException exception)
                {
                    OLogger.Log("Cross Platform Validator Not Implemented: " + exception);
                }
            }
#endif

            // Unlock content from purchases here.
#if USE_PAYOUTS
        if (e.purchasedProduct.definition.payouts != null) {
            OLogger.Log("Purchase complete, paying out based on defined payouts");
            foreach (var payout in e.purchasedProduct.definition.payouts) {
                OLogger.Log(string.Format("Granting {0} {1} {2} {3}", payout.quantity, payout.typeString, payout.subtype, payout.data));
            }
        }
#endif
            // Indicate if we have handled this purchase.
            //   PurchaseProcessingResult.Complete: ProcessPurchase will not be called
            //     with this product again, until next purchase.
            //   PurchaseProcessingResult.Pending: ProcessPurchase will be called
            //     again with this product at next app launch. Later, call
            //     m_Controller.ConfirmPendingPurchase(Product) to complete handling
            //     this purchase. Use to transactionally save purchases to a cloud
            //     game service.
#if DELAY_CONFIRMATION
        StartCoroutine(ConfirmPendingPurchaseAfterDelay(e.purchasedProduct));
        return PurchaseProcessingResult.Pending;
#else

            UnlockContentsAsync(e.purchasedProduct.definition.id).Forget();
            if (!Application.isEditor)
            {
                LogAppsflyerRevenue(e);   
            }
            
            return PurchaseProcessingResult.Complete;
#endif
        }

        private async UniTaskVoid UnlockContentsAsync(string productId)
        {
            await UniTask.SwitchToMainThread();

            onPurchased?.Invoke(true, productId);
            Core.Event.Fire(new OnPurchasedEvent(true, productId));
        }

        private void LogAppsflyerRevenue(PurchaseEventArgs args)
        {
            var product = args.purchasedProduct;
            var productId = product.definition.id;
            var price = (product.metadata.localizedPrice * (decimal)0.65f).ToString();
            var currency = product.metadata.isoCurrencyCode;

            var receiptToJson = JsonConvert.DeserializeObject<Dictionary<string, object>>(product.receipt);
            var receiptPayload =
                JsonConvert.DeserializeObject<Dictionary<string, object>>((string)receiptToJson["Payload"]);
            var transactionID = product.transactionID;

#if UNITY_IOS
            if(isSandbox)
            {
                AppsFlyeriOS.setUseReceiptValidationSandbox(true);
            }

            AppsFlyeriOS.validateAndSendInAppPurchase(productId, price, currency, transactionID, null, this);
#elif UNITY_ANDROID
            var purchaseData = (string)receiptPayload["json"];
            var signature = (string)receiptPayload["signature"];
            AppsFlyer.validateAndSendInAppPurchase(
                "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtaroRbPRtM4fTE6U+LiYpFId2w/hP7PQRyr91r8+5OEKCf05bh4JEm87ZA4Nt/EVNFjYe7GRXcUVYtMCvt1t2fnA/kk1STOInOOOz8iPzEsFnrCf21EibOaMUJXRkDa/YB3gDwyOJ0Kw3AD/g1CnWtG9o1AcNBj7Yef0hbkwNh9UI+rgGkSOUyWqj0kQZcEYnCMauiVP/8rzMTnFE/Zv87BnXO67MAlhRF5y8QzINV1uKjdXzRLyocSXVOMZQjfP814u3jio6D/MQDLxLue/1HCwKSHqyucKlx0adKF45KeuxOF6D/m4ZFIZf/02vsXi9HQZsaevhG86mLyFhDBEvQIDAQAB",
                signature,
                purchaseData,
                price,
                currency,
                null,
                this);

#endif
        }

#if DELAY_CONFIRMATION
    private HashSet<string> m_PendingProducts = new HashSet<string>();

    private IEnumerator ConfirmPendingPurchaseAfterDelay(Product p)
    {
        m_PendingProducts.Add(p.definition.id);
        OLogger.Log("Delaying confirmation of " + p.definition.id + " for 5 seconds.");

		var end = Time.time + 5f;

		while (Time.time < end) {
			yield return null;
			var remaining = Mathf.CeilToInt (end - Time.time);
			UpdateProductPendingUI (p, remaining);
		}

        if (m_IsGooglePlayStoreSelected)
        {
            OLogger.Log("Is " + p.definition.id + " currently owned, according to the Google Play store? "
                      + m_GooglePlayStoreExtensions.IsOwned(p));
        }
        OLogger.Log("Confirming purchase of " + p.definition.id);
        m_Controller.ConfirmPendingPurchase(p);
        m_PendingProducts.Remove(p.definition.id);
		UpdateProductUI (p);
    }
#endif


        /// <summary>
        /// A purchase failed with specified reason.
        /// </summary>
        /// <param name="item">The product that was attempted to be purchased. </param>
        /// <param name="r">The failure reason.</param>
        public void OnPurchaseFailed(Product item, PurchaseFailureDescription failureDescription)
        {
            OLogger.LogError(
                $"Purchase failed: {failureDescription.productId} - {failureDescription.reason} - {failureDescription.message}");

            // Detailed debugging information
            OLogger.Log("Store specific error code: " +
                        _transactionHistoryExtensions.GetLastStoreSpecificPurchaseErrorCode());
            if (_transactionHistoryExtensions.GetLastPurchaseFailureDescription() != null)
            {
                OLogger.Log("Purchase failure description message: " +
                            _transactionHistoryExtensions.GetLastPurchaseFailureDescription().message);
            }

            _purchaseInProgress = false;

            onPurchased?.Invoke(false, item.definition.id);
            Core.Event.Fire(new OnPurchasedEvent(false, item.definition.id));
        }

        public void OnPurchaseFailed(Product product, PurchaseFailureReason failureReason)
        {
            OLogger.LogError($"Purchase failed: {product.definition.id} - Reason: {failureReason}");

            _purchaseInProgress = false;

            // onPurchased?.Invoke(false, product.definition.id);
        }

        /// <summary>
        /// Purchasing failed to initialise for a non-recoverable reason.
        /// </summary>
        /// <param name="error"> The failure reason. </param>
        public void OnInitializeFailed(InitializationFailureReason error)
        {
            OLogger.Log("Billing failed to initialize!");
            switch (error)
            {
                case InitializationFailureReason.AppNotKnown:
                    OLogger.LogError("Is your App correctly uploaded on the relevant publisher console?");
                    break;
                case InitializationFailureReason.PurchasingUnavailable:
                    // Ask the user if billing is disabled in device settings.
                    OLogger.Log("Billing disabled!");
                    break;
                case InitializationFailureReason.NoProductsAvailable:
                    // Developer configuration error; check product metadata.
                    OLogger.Log("No products available for purchase!");
                    break;
            }
        }

        /// <summary>
        /// This will be called after a call to IAppleExtensions.RestoreTransactions().
        /// </summary>
        private void OnTransactionsRestored(bool success, string message)
        {
            OLogger.Log("Transactions restored." + success);
        }

        /// <summary>
        /// iOS Specific.
        /// This is called as part of Apple's 'Ask to buy' functionality,
        /// when a purchase is requested by a minor and referred to a parent
        /// for approval.
        ///
        /// When the purchase is approved or rejected, the normal purchase events
        /// will fire.
        /// </summary>
        /// <param name="item">Item.</param>
        private void OnDeferred(Product item)
        {
            OLogger.Log("Purchase deferred: " + item.definition.id);
        }

#if INTERCEPT_PROMOTIONAL_PURCHASES
    private void OnPromotionalPurchase(Product item) {
        OLogger.Log("Attempted promotional purchase: " + item.definition.id);

        // Promotional purchase has been detected. Handle this event by, e.g. presenting a parental gate.
        // Here, for demonstration purposes only, we will wait five seconds before continuing the purchase.
        StartCoroutine(ContinuePromotionalPurchases());
    }

    private IEnumerator ContinuePromotionalPurchases()
    {
        OLogger.Log("Continuing promotional purchases in 5 seconds");
        yield return new WaitForSeconds(5);
        OLogger.Log("Continuing promotional purchases now");
        m_AppleExtensions.ContinuePromotionalPurchases (); // iOS and tvOS only; does nothing on Mac
    }
#endif

        /// <summary>
        /// Triggered when the user presses the <c>Buy</c> button on a product user interface component.
        /// </summary>
        /// <param name="productID">The product identifier to buy</param>
        public void Purchase(string productID)
        {
            OLogger.Log($"PurchaseSystem.Purchase productId {productID}");

            if (_purchaseInProgress == true)
            {
                OLogger.Log("Please wait, purchase in progress");
                onPurchased?.Invoke(false, productID);
                Core.Event.Fire(new OnPurchasedEvent(false, productID));
                return;
            }

            if (_controller == null)
            {
                OLogger.LogError("Purchasing is not initialized");
                onPurchased?.Invoke(false, productID);
                Core.Event.Fire(new OnPurchasedEvent(false, productID));
                return;
            }

            if (_controller.products.WithID(productID) == null)
            {
                OLogger.LogError("No product has id " + productID);
                onPurchased?.Invoke(false, productID);
                Core.Event.Fire(new OnPurchasedEvent(false, productID));
                return;
            }

            // Don't need to draw our UI whilst a purchase is in progress.
            // This is not a requirement for IAP Applications but makes the demo
            // scene tidier whilst the fake purchase dialog is showing.
            _purchaseInProgress = true;

            //Sample code how to add accountId in developerPayload to pass it to getBuyIntentExtraParams
            //Dictionary<string, string> payload_dictionary = new Dictionary<string, string>();
            //payload_dictionary["accountId"] = "Faked account id";
            //payload_dictionary["developerPayload"] = "Faked developer payload";
            //m_Controller.InitiatePurchase(m_Controller.products.WithID(productID), MiniJson.JsonEncode(payload_dictionary));
            _controller.InitiatePurchase(_controller.products.WithID(productID), "developerPayload");
        }

        /// <summary>
        /// Triggered when the user presses the restore button.
        /// </summary>
        public void RestorePurchase()
        {
            // if (m_IsSamsungAppsStoreSelected)
            // {
            //     m_SamsungExtensions.RestoreTransactions(OnTransactionsRestored);
            // }
            if (Application.platform == RuntimePlatform.WSAPlayerX86 ||
                Application.platform == RuntimePlatform.WSAPlayerX64 ||
                Application.platform == RuntimePlatform.WSAPlayerARM)
            {
                _microsoftExtensions.RestoreTransactions();
            }
            else if (_isGooglePlayStoreSelected)
            {
                _googlePlayStoreExtensions.RestoreTransactions(OnTransactionsRestored);
            }
            else
            {
                _appleExtensions.RestoreTransactions(OnTransactionsRestored);
            }
        }

        public string GetLocalizedPriceString(string productID)
        {
            if (_controller == null)
            {
                OLogger.LogError("Can not get localized price! Store have not initialized correctly!");
                return "";
            }

            if (_controller.products.WithID(productID) == null)
            {
                OLogger.LogNotice("No product has id " + productID);
                return "";
            }

            return _controller.products.WithID(productID).metadata.localizedPriceString;
        }

        public decimal GetLocalizedPrice(string productID)
        {
            if (_controller == null)
            {
                OLogger.LogError("Can not get localized price! Store have not initialized correctly!");
                return 0;
            }

            if (_controller.products.WithID(productID) != null)
                return _controller.products.WithID(productID).metadata.localizedPrice;
            OLogger.LogNotice("No product has id " + productID);
            return 0;
        }

        public string GetIsoCurrencyCode(string productID)
        {
            if (_controller == null)
            {
                OLogger.LogError("Can not get localized price! Store have not initialized correctly!");
                return string.Empty;
            }

            if (_controller.products.WithID(productID) != null)
                return _controller.products.WithID(productID).metadata.isoCurrencyCode;
            OLogger.LogNotice("No product has id " + productID);
            return string.Empty;
        }

        public string FormatPrice(decimal newPrice, string isoCurrencyCode, bool isOldPrice)
        {
            var oldPrice = newPrice * 2;

            var culture = CultureInfo.GetCultures(CultureTypes.SpecificCultures)
                .FirstOrDefault(c =>
                {
                    try
                    {
                        var region = new RegionInfo(c.LCID);
                        return region.ISOCurrencySymbol.Equals(isoCurrencyCode, StringComparison.OrdinalIgnoreCase);
                    }
                    catch
                    {
                        return false;
                    }
                }) ?? CultureInfo.InvariantCulture;

            return isOldPrice ? oldPrice.ToString("C", culture) : newPrice.ToString("C", culture);
        }

        public Product GetProduct(string productId) => _controller.products.WithID(productId);

        public SubscriptionInfo GetSubscriptionInfo(string productId)
        {
            var item = GetProduct(productId);
            if (item?.receipt == null) return null;
            if (item.definition.type != ProductType.Subscription)
                return null;

            var p = new SubscriptionManager(item, null);
            return p.getSubscriptionInfo();
        }

        private bool NeedRestoreButton()
        {
            return Application.platform == RuntimePlatform.IPhonePlayer ||
                   Application.platform == RuntimePlatform.OSXPlayer ||
                   Application.platform == RuntimePlatform.tvOS ||
                   Application.platform == RuntimePlatform.WSAPlayerX86 ||
                   Application.platform == RuntimePlatform.WSAPlayerX64 ||
                   Application.platform == RuntimePlatform.WSAPlayerARM;
        }

        private void LogProductDefinitions()
        {
            var products = _controller.products.all;
            foreach (var product in products)
            {
                OLogger.Log(
                    $"id: {product.definition.id}\nstore-specific id: {product.definition.storeSpecificId}\ntype: {product.definition.type.ToString()}\nenabled: {(product.definition.enabled ? "enabled" : "disabled")}");
            }
        }
    }
}
#endif