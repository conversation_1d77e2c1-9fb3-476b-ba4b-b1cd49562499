using System;
using UnityEngine;

namespace _FeatureHub.Tutorial.Structures
{
    public struct WrapperVector : IDisposable
    {
        private float x;
        private float y;
        private float z;
        public static implicit operator Vector2(WrapperVector source) => new(source.x, source.y);
        public static implicit operator Vector3(WrapperVector source) => new(source.x, source.y, source.z);
        
        public static implicit operator WrapperVector(Vector2 source) => new() { x = source.x, y = source.y, z = 0 };
        public static implicit operator WrapperVector(Vector3 source) => new() { x = source.x, y = source.y, z = source.z };
        
        public static implicit operator <PERSON>rapper<PERSON>ector(float value) => new() { x = value, y = value, z = value};

        public static WrapperVector operator +(WrapperVector a, WrapperVector b) => new() { x = a.x + b.x, y = a.y + b.y, z = a.z + b.z };

        void IDisposable.Dispose() { }
    }
}