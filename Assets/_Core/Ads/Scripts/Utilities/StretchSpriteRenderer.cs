using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[RequireComponent(typeof(SpriteRenderer))]
public class StretchSpriteRenderer : MonoBehaviour
{
    [SerializeField] Camera _camera;

    void Start()
    {
        if (_camera == null)
            _camera = Camera.main;

        var renderer = GetComponent<SpriteRenderer>();
        var textureRect = renderer.sprite.textureRect;
        var textureWidthInUnit = textureRect.width / renderer.sprite.pixelsPerUnit;
        var textureHeightInUnit = textureRect.height / renderer.sprite.pixelsPerUnit;
        var textureRatio = (float)textureRect.width / (float)textureRect.height;

        var screenHeightInUnit = _camera.orthographicSize * 2f;
        var screenWidthInUnit = _camera.aspect * screenHeightInUnit;

        var scaleFactor = 1f;        
        if (_camera.aspect >= textureRatio) // Match screen width
        {
            scaleFactor = screenWidthInUnit / textureWidthInUnit;
        }
        else // Match screen height
        {
            scaleFactor = screenHeightInUnit / textureHeightInUnit;
        }

        scaleFactor *= 1.05f;
        transform.localScale = Vector3.one * scaleFactor;
    }
}
