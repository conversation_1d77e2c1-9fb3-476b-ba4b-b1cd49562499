using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Runtime.CompilerServices;
using System.Text;
using OnePuz;
using Firebase.Crashlytics;
using UnityEngine;
using UnityEngine.CrashReportHandler;

public static class OLogger
{
    private static readonly StringBuilder _builder = new();

    private static bool _enableLog = false;
    private static bool _checkedDebugger = false;

    private static bool EnableLog
    {
        get
        {
            if (_checkedDebugger)
                return _enableLog;

            _checkedDebugger = true;
#if UNITY_EDITOR
            _enableLog = true;
#else
            _enableLog = SRDebugger.Settings.Instance.IsEnabled;
#endif
            return _enableLog;
        }
    }

    public static void Log(string message, [CallerFilePath] string file = null, [CallerMemberName] string method = null)
    {
        _builder.Clear();
        if (Application.isEditor)
        {
            _builder.AppendFormat("[{0}.<color=orange>{1}</color>()]: ", Path.GetFileNameWithoutExtension(file), method);
            _builder.AppendFormat("<color=white>{0}</color>", message);
        }
        else
        {
            // _builder.AppendFormat("[{0}.{1}()]: ", Path.GetFileNameWithoutExtension(file), method);
            _builder.Append(message);
        }

        CrashlyticsLog(_builder.ToString());

        if (EnableLog)
            Debug.Log(_builder.ToString());
    }
    
    public static void LogDebug(string message, [CallerFilePath] string file = null, [CallerMemberName] string method = null)
    {
        _builder.Clear();
        if (Application.isEditor)
        {
            _builder.AppendFormat("[{0}.<color=orange>{1}</color>()]: ", Path.GetFileNameWithoutExtension(file), method);
            _builder.AppendFormat("<color=white>{0}</color>", message);
        }
        else
        {
            _builder.Append(message);
        }

        if (EnableLog)
            Debug.Log(_builder.ToString());
    }

    public static void LogNotice(string message, [CallerFilePath] string file = null, [CallerMemberName] string method = null)
    {
        _builder.Clear();
        if (Application.isEditor)
        {
            _builder.AppendFormat("[{0}.<color=orange>{1}</color>()]: ", Path.GetFileNameWithoutExtension(file), method);
            _builder.AppendFormat("<color=lime>{0}</color>", message);
        }
        else
        {
            // _builder.AppendFormat("[{0}.{1}()]: ", Path.GetFileNameWithoutExtension(file), method);
            _builder.Append(message);
        }

        CrashlyticsLog(_builder.ToString());

        if (EnableLog)
            Debug.Log(_builder.ToString());
    }

    // public static void Log(params object[] data)
    // {
    //     var sb = new System.Text.StringBuilder();
    //
    //     for (var i = 0; i < data.Length; i++)
    //     {
    //         sb.Append(data[i].ToString());
    //         sb.Append(" ");
    //     }
    //
    //     var message = sb.ToString();
    //     Log(message);
    // }

    public static void Log(List<int> data)
    {
        _builder.Clear();

        for (var i = 0; i < data.Count; i++)
        {
            _builder.Append(data[i].ToString());
            _builder.Append(" - ");
        }

        var message = _builder.ToString();
        Log(message);
    }

    public static void LogError(string message, [CallerFilePath] string file = null, [CallerMemberName] string method = null)
    {
        _builder.Clear();
        if (Application.isEditor)
        {
            _builder.AppendFormat("[{0}.{1}()]: ", Path.GetFileName(file), method);
            _builder.Append(message);
        }
        else
        {
            _builder.Append(message);
        }

        CrashlyticsLog(_builder.ToString(), true);

        if (EnableLog)
            Debug.LogError(_builder.ToString());
    }

    public static void LogError(params object[] data)
    {
        _builder.Clear();
        for (var i = 0; i < data.Length; i++)
        {
            _builder.Append(data[i].ToString());
            _builder.Append(" ");
        }

        var message = _builder.ToString();
        LogError(message);
    }

    public static void LogWarning(string message)
    {
        CrashlyticsLog(message);

        if (EnableLog)
            Debug.LogWarning(message);
    }

    public static void LogWarning(params object[] data)
    {
        _builder.Clear();
        for (var i = 0; i < data.Length; i++)
        {
            _builder.Append(data[i].ToString());
            _builder.Append(" ");
        }

        var message = _builder.ToString();
        LogWarning(message);
    }

    private static void CrashlyticsLog(string message, bool isError = false)
    {
#if (UNITY_ANDROID || UNITY_IOS) && !UNITY_EDITOR
        Crashlytics.Log(message);
#endif
    }

    public static void CrashlyticsSetKey(string key, string value)
    {
#if (UNITY_ANDROID || UNITY_IOS) && !UNITY_EDITOR
        if (Firebase.FirebaseApp.DefaultInstance != null)
            Crashlytics.SetCustomKey(key, value);
#endif

        CrashReportHandler.SetUserMetadata(key, value);
    }

    public static void CrashlyticsSetUserId(string identifier)
    {
#if (UNITY_ANDROID || UNITY_IOS) && !UNITY_EDITOR
        if (Firebase.FirebaseApp.DefaultInstance != null)
            Crashlytics.SetUserId(identifier);
#endif
    }

    public static void CrashlyticsException(System.Exception e)
    {
#if (UNITY_ANDROID || UNITY_IOS) && !UNITY_EDITOR
        if (Firebase.FirebaseApp.DefaultInstance != null)
            Crashlytics.LogException(e);
#endif
    }

    public static void Log(string title, List<int> listInt)
    {
        _builder.Clear();
        _builder.Append(title).Append(": ");
        for (var i = 0; i < listInt.Count; i++)
        {
            _builder.Append(listInt[i]).Append((i == listInt.Count - 1) ? "" : " - ");
        }

        Log(_builder.ToString());
    }

    public static void Log(string title, int[] arrayInt)
    {
        _builder.Clear();
        _builder.Append(title).Append(": ");
        for (var i = 0; i < arrayInt.Length; i++)
        {
            _builder.Append(arrayInt[i]);
            if (i < arrayInt.Length - 1)
                _builder.Append(" - ");
        }

        Log(_builder.ToString());
    }
}