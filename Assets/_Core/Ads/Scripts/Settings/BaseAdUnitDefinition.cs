using System;
using System.Text;
using System.Linq;
using Sirenix.OdinInspector;
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
using UnityEditor.Build;
#endif

namespace OnePuz.Ads
{
    public enum AdProvider
    {
        ADMOB,
        UNITY_ADS,
        FACEBOOK_AUDIENCE_NETWORK,
        IRON_SOURCE,
        APPLOVIN
    }

    [CreateAssetMenu(menuName = "OnePuz/Base Ad Unit Setting")]
    public class BaseAdUnitDefinition : ScriptableObject
    {
        private const string ADMOB_DOCUMENT_URL = "https://developers.google.com/admob/unity/quick-start";
        private const string UNITY_ADS_DOCUMENT_URL = "https://unityads.unity3d.com/help/monetization/getting-started";
        private const string FAN_DOCUMENT_URL = "https://developers.facebook.com/docs/audience-network/overview";
        private const string IRONSOURCE_DOCUMENT_URL = "https://developers.ironsrc.com/ironsource-mobile/unity/unity-plugin/#step-1";
        private const string APPLOVIN_DOCUMENT_URL = "https://dash.applovin.com/documentation/mediation/unity/getting-started";

        private const string ADMOB_DEFINE_SYMBOL = "USING_ADMOB";
        private const string UNITY_ADS_DEFINE_SYMBOL = "USING_UNITY_ADS";
        private const string FAN_DEFINE_SYMBOL = "USING_FAN";
        private const string IRONSOURCE_DEFINE_SYMBOL = "USING_IRONSOURCE";
        private const string APPLOVIN_DEFINE_SYMBOL = "USING_APPLOVIN";

        [PropertyOrder(1000)]
        [PropertySpace(SpaceBefore = 10, SpaceAfter = 10)]
        [InfoBox("Please check out official document before setting ads")]
        [Button]
        public void OpenOfficialDocument()
        {
            if (Provider == AdProvider.ADMOB)
                Application.OpenURL(ADMOB_DOCUMENT_URL);
            else if (Provider == AdProvider.UNITY_ADS)
                Application.OpenURL(UNITY_ADS_DOCUMENT_URL);
            else if (Provider == AdProvider.FACEBOOK_AUDIENCE_NETWORK)
                Application.OpenURL(FAN_DOCUMENT_URL);
            else if (Provider == AdProvider.IRON_SOURCE)
                Application.OpenURL(IRONSOURCE_DOCUMENT_URL);
            else if (Provider == AdProvider.APPLOVIN)
                Application.OpenURL(APPLOVIN_DOCUMENT_URL);
        }

        [HideInInspector]
        public bool UsingThisNetwork;

        [HideIf("UsingThisNetwork")]
        [PropertySpace(SpaceBefore = 5, SpaceAfter = 10)]
        [PropertyOrder(-1)]
        [Button(ButtonSizes.Large), GUIColor(0, 1, 0)]
        public void EnableNetwork()
        {
            UsingThisNetwork = true;

#if UNITY_EDITOR
#if UNITY_6000_0_OR_NEWER
#if UNITY_ANDROID
            NamedBuildTarget targetGroup = NamedBuildTarget.Android;
#elif UNITY_IOS
            NamedBuildTarget targetGroup = NamedBuildTarget.iOS;
#endif
#else
            #if UNITY_ANDROID
            BuildTargetGroup targetGroup = BuildTargetGroup.Android;
#elif UNITY_IOS
            BuildTargetGroup targetGroup = BuildTargetGroup.iOS;
#endif
#endif
            var symbols = PlayerSettings.GetScriptingDefineSymbols(targetGroup);
            var symbolList = symbols.Split(';').ToList();
            var providerSymbol = GetDefineSymbolByProvider();
            if (!string.IsNullOrEmpty(providerSymbol))
            {
                var containSymbol = false;
                for (var i = 0; i < symbolList.Count; i++)
                {
                    if (string.CompareOrdinal(symbolList[i], providerSymbol) == 0)
                    {
                        containSymbol = true;
                    }
                }

                if (!containSymbol)
                {
                    symbolList.Add(providerSymbol);
                    var builder = new StringBuilder();
                    for (var i = 0; i < symbolList.Count; i++)
                    {
                        builder.Append(symbolList[i]).Append(";");

                        if (i == symbolList.Count - 1)
                            builder.Length--;
                    }

                    PlayerSettings.SetScriptingDefineSymbols(targetGroup, builder.ToString());
                }
            }
#endif
        }

        [ShowIf("UsingThisNetwork")]
        [PropertySpace(SpaceBefore = 5, SpaceAfter = 10)]
        [PropertyOrder(-1)]
        [Button(ButtonSizes.Large), GUIColor(1, 1, 0)]
        public void DisableNetwork()
        {
            UsingThisNetwork = false;

#if UNITY_EDITOR
#if UNITY_6000_0_OR_NEWER
#if UNITY_ANDROID
            NamedBuildTarget targetGroup = NamedBuildTarget.Android;
#elif UNITY_IOS
            NamedBuildTarget targetGroup = NamedBuildTarget.iOS;
#endif
#else
            #if UNITY_ANDROID
            BuildTargetGroup targetGroup = BuildTargetGroup.Android;
#elif UNITY_IOS
            BuildTargetGroup targetGroup = BuildTargetGroup.iOS;
#endif
#endif
            var symbols = PlayerSettings.GetScriptingDefineSymbols(targetGroup);
            var symbolList = symbols.Split(';').ToList();
            var providerSymbol = GetDefineSymbolByProvider();
            if (!string.IsNullOrEmpty(providerSymbol))
            {
                for (var i = symbolList.Count - 1; i >= 0; i--)
                {
                    if (string.CompareOrdinal(symbolList[i], providerSymbol) == 0)
                    {
                        symbolList.RemoveAt(i);
                    }
                }

                var builder = new StringBuilder();
                for (var i = 0; i < symbolList.Count; i++)
                {
                    builder.Append(symbolList[i]).Append(";");

                    if (i == symbolList.Count - 1)
                        builder.Length--;
                }

                PlayerSettings.SetScriptingDefineSymbols(targetGroup, builder.ToString());
            }
#endif
        }

        [EnableIf("UsingThisNetwork")]
        [BoxGroup("Properties")]
        public AdProvider Provider;

        public string ProviderToString
        {
            get
            {
                if (Provider == AdProvider.ADMOB)
                    return "Admob";
                else if (Provider == AdProvider.FACEBOOK_AUDIENCE_NETWORK)
                    return "Fan";
                else if (Provider == AdProvider.IRON_SOURCE)
                    return "IronSource";
                else if (Provider == AdProvider.UNITY_ADS)
                    return "UnityAds";
                else if (Provider == AdProvider.APPLOVIN)
                    return "Applovin";
                else
                    return "Unknown";
            }
        }

        [EnableIf("UsingThisNetwork")]
        [BoxGroup("Properties")]
        public string GameId;

        [EnableIf("UsingThisNetwork")]
        [BoxGroup("Properties")]
        public string BannerId;

        [EnableIf("UsingThisNetwork")]
        [BoxGroup("Properties")]
        public string InterstitialId;

        [EnableIf("UsingThisNetwork")]
        [BoxGroup("Properties")]
        public string RewardedVideoId;

        [EnableIf("UsingThisNetwork")]
        [BoxGroup("Properties")]
        public string AppOpenAdId;

        private string GetDefineSymbolByProvider()
        {
            if (Provider == AdProvider.ADMOB)
                return ADMOB_DEFINE_SYMBOL;
            else if (Provider == AdProvider.FACEBOOK_AUDIENCE_NETWORK)
                return FAN_DEFINE_SYMBOL;
            else if (Provider == AdProvider.IRON_SOURCE)
                return IRONSOURCE_DEFINE_SYMBOL;
            else if (Provider == AdProvider.UNITY_ADS)
                return UNITY_ADS_DEFINE_SYMBOL;
            else if (Provider == AdProvider.APPLOVIN)
                return APPLOVIN_DEFINE_SYMBOL;
            else
                return "";
        }
    }
}