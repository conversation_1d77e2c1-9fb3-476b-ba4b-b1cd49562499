using OnePuz.Shared;
using UnityEngine;

namespace OnePuz.OPTimeline
{

    [System.Serializable]
    [OPClipCreate("AC Animator/Timeline", "Custom Timeline")]
    public class ClipAnimatorTimeline : OPClip
    {
        [SerializeField, CheckForNull] private OPAnimator target;

        protected override void OnStateChanged(OPEvaluateState currentState, OPEvaluateState lastState) { }

        protected override void OnEnter()
        {
            foreach (var clip in target.GetClips())
            {
                clip.OnTimelineStarted();
            }
        }

        protected override void OnUpdate(OPEvaluateState state, float timelineTime, float clipTime,
            float normalizedClipTime, bool previewMode)
        {
            if (state != OPEvaluateState.Running) return;
            target.Evaluate(normalizedClipTime * target.FullDuration, previewMode);
        }

        protected override void OnExit()
        {
            foreach (var clip in target.GetClips())
            {
                clip.OnTimelineCompleted();
            }
        }

        public override void OnTimelineStarted()
        {

        }

        public override void OnTimelineCompleted()
        {

        }

        public override void OnPreviewModeChanged(bool previewMode)
        {
            if (!IsValid()) return;
            foreach (var opClip in target.GetClips())
            {
                opClip.OnPreviewModeChanged(previewMode);
            }
        }

        public override void OnClipAddedToOPAnimator(OPAnimator animator)
        {
        }

        public override void OnAnimatorStartPlaying()
        {
        }

        public override void OnAnimatorCompletePlaying()
        {
        }

        public override bool CanBePlayedInPreviewMode()
        {
            return true;
        }

        public override bool IsValid()
        {
            return target != null;
        }

        [OMCustomButton("Reset Timeline Duration")]
        public void CustomButton()
        {
            if (!IsValid()) return;
            Duration = target.FullDuration;
        }

        public override Component GetTarget()
        {
            return target;
        }

        public override void SetTarget(GameObject newTarget)
        {
            this.target = newTarget.GetComponent<OPAnimator>();
        }
    }
}