using OnePuz.OPEase;
using Unity.Mathematics;
using UnityEngine;
using UnityEngine.Splines;

namespace OnePuz.OPTimeline
{
    [System.Serializable]
    [OPClipCreate("RectTransform/Spline Anchor Position", "Spline Anchor Position")]
    public class ClipSplineAnchorPosition : OPClip
    {
        [SerializeField, CheckForNull] private SplineContainer _path;
        [SerializeField, CheckForNull] private RectTransform _target;
        [SerializeField] protected EaseData _ease;

        [SerializeField] private OPClipInitializer<Vector2> initializer;

        private RectTransform _pathRectTransform;
        private RectTransform pathRectTransform => _pathRectTransform ?? (_pathRectTransform = _path.GetComponent<RectTransform>());
        private RectTransform _targetParentRectTransform;
        private RectTransform targetParentRectTransform => _targetParentRectTransform ?? (_targetParentRectTransform = _target.parent?.GetComponent<RectTransform>());

        private Vector2 _storedAnchorPosition;

        protected override void OnStateChanged(OPEvaluateState currentState, OPEvaluateState lastState) { }

        protected override void OnEnter()
        {
            _pathRectTransform = _path.GetComponent<RectTransform>();
            _targetParentRectTransform = _target.parent.GetComponent<RectTransform>();
        }

        protected override void OnUpdate(OPEvaluateState state, float timelineTime, float clipTime,
            float normalizedClipTime, bool previewMode)
        {
            if (state != OPEvaluateState.Running) return;
            if (_path == null || _target == null) return;

            // Move the UI element
            _target.anchoredPosition = GetSplinePosition(normalizedClipTime);
            // Debug.Log("ClipSplineAnchorPosition: " + _target.anchoredPosition);
        }

        private float2 GetSplinePosition(float normalizedClipTime)
        {
            // Sample the position along the spline
            var offsetAnchorPosition = (float2)pathRectTransform.anchoredPosition - (targetParentRectTransform == null ? float2.zero : (float2)targetParentRectTransform.anchoredPosition);
            return _path.Spline.EvaluatePosition(_ease.Lerp(normalizedClipTime)).xy + offsetAnchorPosition;
        }

        protected override void OnExit()
        {

        }

        public override void OnTimelineStarted()
        {
            initializer.Initialize(() =>
            {
                _target.anchoredPosition = initializer.Value;
            });
        }

        public override void OnTimelineCompleted()
        {

        }

        public override void OnPreviewModeChanged(bool previewMode)
        {
            if (!IsValid()) return;

            if (previewMode)
            {
                _storedAnchorPosition = _target.anchoredPosition;
                initializer.Initialize(() =>
                {
                    _target.anchoredPosition = initializer.Value;
                });
            }
            else
            {
                _target.anchoredPosition = _storedAnchorPosition;
            }

        }

        public override void OnClipAddedToOPAnimator(OPAnimator animator)
        {
        }

        public override void OnAnimatorStartPlaying()
        {
        }

        public override void OnAnimatorCompletePlaying()
        {
        }

        public override bool CanBePlayedInPreviewMode()
        {
            return true;
        }

        public override bool IsValid()
        {
            return _target != null;
        }

        public override Component GetTarget()
        {
            return _target;
        }

        public override void SetTarget(GameObject newTarget)
        {
            this._target = newTarget.GetComponent<RectTransform>();
        }
    }
}