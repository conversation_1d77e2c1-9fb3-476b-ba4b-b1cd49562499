using UnityEngine;
using UnityEngine.Events;

namespace OnePuz.OPTimeline
{
    [System.Serializable]
    [OPClipCreate("AC Animator/Event", "Event")]
    public class ClipEvent : OPClip
    {
        protected override void OnStateChanged(OPEvaluateState currentState, OPEvaluateState lastState) { }

        protected override void OnEnter() { }

        protected override void OnUpdate(OPEvaluateState state, float timelineTime, float clipTime, float normalizedClipTime, bool previewMode) { }

        protected override void OnExit() { }

        public override void OnTimelineStarted() { }

        public override void OnTimelineCompleted() { }

        public override void OnPreviewModeChanged(bool previewMode) { }

        public override void OnClipAddedToOPAnimator(OPAnimator animator) { }

        public override void OnAnimatorStartPlaying() { }

        public override void OnAnimatorCompletePlaying() { }

        public override bool CanBePlayedInPreviewMode()
        {
            return false;
        }
        
        public override bool IsValid()
        {
            return true;
        }
        
        public override Component GetTarget()
        {
            return null;
        }

        public override void SetTarget(GameObject newTarget)
        {
            
        }
    }
}