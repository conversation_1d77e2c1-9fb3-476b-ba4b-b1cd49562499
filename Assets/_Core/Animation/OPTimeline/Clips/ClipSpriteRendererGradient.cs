using OnePuz.Shared;
using UnityEngine;

namespace OnePuz.OPTimeline
{
    [System.Serializable]
    [OPClipCreate("SpriteRenderer/Gradient", "SpriteRenderer Gradient")]
    public class ClipSpriteRendererGradient : OPClipCore<Color>
    {
        [SerializeField,CheckForNull] private SpriteRenderer target;
        [SerializeField] private UnityEngine.Gradient gradient;
        
        public override bool IsValid()
        {
            return target != null;
        }

        protected override Color GetCurrentValue()
        {
            return target.color;
        }

        protected override void SetValue(Color newValue)
        {
            target.color = newValue;
        }
        
        protected override void OnUpdate(OPEvaluateState state, float timelineTime, float clipTime,
            float normalizedClipTime, bool previewMode)
        {
            if(state != OPEvaluateState.Running) return;
            SetValue(gradient.Evaluate(normalizedClipTime));
        }
        
        public override void OnClipAddedToOPAnimator(OPAnimator animator)
        {
            if(target == null) target = animator.GetComponent<SpriteRenderer>();
        }
        
        public override Component GetTarget()
        {
            return target;
        }
        
        public override void SetTarget(GameObject newTarget)
        {
            this.target = newTarget.GetComponent<SpriteRenderer>();
        }
    }
}