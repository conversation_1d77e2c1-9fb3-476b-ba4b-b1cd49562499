using UnityEngine;
using UnityEngine.UI;

namespace OnePuz.OPTimeline
{
    [System.Serializable]
    [OPClipCreate("Graphic/Alpha", "Graphic Alpha")]
    public class ClipGraphicAlpha : OPTweenClip<float,Graphic>
    {
        protected override float GetCurrentValue()
        {
            return target.color.a;
        }

        protected override void SetValue(float newValue)
        {
            var color = target.color;
            color.a = newValue;
            target.color = color;
        }
        
        protected override void OnUpdate(OPEvaluateState state, float timelineTime, float clipTime,
            float normalizedClipTime, bool previewMode)
        {
            if(state != OPEvaluateState.Running) return;
            SetValue(Mathf.LerpUnclamped(CurrentFrom, to, ease.Lerp(normalizedClipTime)));
        }
    }
}