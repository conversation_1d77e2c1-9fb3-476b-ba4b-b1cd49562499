using System;

namespace OnePuz.OPTimeline
{
    /// <summary>
    /// The Attribute for the clip to create the clip in the Search Window of the OPAnimator
    /// </summary>
    [AttributeUsage(AttributeTargets.Class)]
    public class OPClipCreateAttribute : Attribute
    {
        /// <summary>
        /// The Menu Name of the Clip
        /// </summary>
        public string ClipMenuName { get; }
        
        /// <summary>
        /// The Clip Name of the Clip
        /// </summary>
        public string ClipName { get; }
        
        public OPClipCreateAttribute(string clipMenuName = null, string clipName = null)
        {
            ClipMenuName = clipMenuName;
            ClipName = clipName;
        }
        
        
    }
}