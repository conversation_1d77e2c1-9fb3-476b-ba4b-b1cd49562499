using System.Collections.Generic;
using System.Linq;
using OnePuz.Shared;
using UnityEngine;

namespace OnePuz.OPTimeline
{
    /// <summary>
    /// The base class for all OP Animators
    /// Here you can add your own clips and evaluate them
    /// </summary>
    [OMTitle("OP Animator")]
    public abstract class OPAnimator : MonoBehaviour
    {
        [SerializeField,Min(.1f)] private float fullDuration = 1;
        [SerializeField,Min(0)] private float speed = 1;
        [SerializeField] private bool timeIndependent = false;
        [SerializeField] private bool playOnEnable = false;
        [SerializeReference] private List<OPClip> clips;
        
        public float TimelineTime { get; set; }
        public float FullDuration { get => fullDuration; set => fullDuration = Mathf.Max(value,.1f); }
        public float Speed { get => speed; set => speed = value; }
        public bool TimeIndependent { get => timeIndependent; set => timeIndependent = value; }
        public bool PlayOnEnable => playOnEnable;

        /// <summary>
        /// Simulate the Animator by delta time and speed
        /// </summary>
        /// <param name="previewMode"></param>
        public void Simulate(bool previewMode)
        {
            TimelineTime = Mathf.Clamp(TimelineTime + (TimeIndependent? Time.unscaledDeltaTime : Time.deltaTime) * Speed,0,fullDuration);
            Evaluate(TimelineTime,previewMode);
        }

        /// <summary>
        /// Evaluate the Animator 
        /// </summary>
        /// <param name="time">the actual time</param>
        /// <param name="previewMode">whether or not the it is in preview mode</param>
        public void Evaluate(float time,bool previewMode)
        {
            TimelineTime = Mathf.Clamp(time,0, fullDuration);
            foreach (var clip in GetClips())
            {
                if (!clip.IsValid()) continue;
                clip.Evaluate(TimelineTime,previewMode);
            }
        }
        
        /// <summary>
        /// Get All Clips in the Animator
        /// </summary>
        /// <returns></returns>
        public List<OPClip> GetClips()
        {
            if(clips == null) clips = new List<OPClip>();
            return clips;
        }

        /// <summary>
        /// Get the Last Clip in the Timeline based on the End Time
        /// </summary>
        /// <returns></returns>
        public OPClip GetTimelineLastClip()
        {
            if (GetClips().Count <= 0) return null;
            return GetClips().OrderBy(x => x.GetEndTime()).Last();
        }
    }
}