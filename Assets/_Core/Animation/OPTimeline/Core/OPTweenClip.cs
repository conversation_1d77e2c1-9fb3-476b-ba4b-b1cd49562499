using OnePuz.OPEase;
using UnityEngine;

namespace OnePuz.OPTimeline
{
    public enum OPFromType
    {
        CustomFrom,
        CurrentValue
    }
    /// <summary>
    /// base class for all tween clips
    /// </summary>
    /// <typeparam name="TType">The type of the Value such as Vector3, float...</typeparam>
    /// <typeparam name="TTarget">the type of the target such as Transform, Image, CanvasGroup...</typeparam>
    [System.Serializable]
    public abstract class OPTweenClip<TType, TTarget> : OPClipCore<TType> where TType : struct where TTarget : Component
    {
        [Serialize<PERSON>ield, CheckForNull] protected TTarget target;
        [SerializeField] protected EaseData ease;
        [SerializeField] protected OPFromType fromType = OPFromType.CustomFrom;
        [SerializeField] protected TType from;
        [SerializeField] protected TType to;

        protected TType CurrentFrom { get; set; }
        internal TType FromValue => from;

        public override void OnClipAddedToOPAnimator(OPAnimator animator)
        {
            if (target == null) target = animator.GetComponent<TTarget>();
        }

        protected override void OnEnter()
        {
            CurrentFrom = fromType == OPFromType.CustomFrom ? from : GetCurrentValue();
        }

        public override bool IsValid()
        {
            return target != null;
        }

        public override Component GetTarget()
        {
            return target;
        }

        public override void SetTarget(GameObject newTarget)
        {
            this.target = newTarget.GetComponent<TTarget>();
        }

        public void ApplyValueFrom()
        {
            from = GetCurrentValue();
        }

        public void ApplyValueTo()
        {
            to = GetCurrentValue();
        }
    }
}