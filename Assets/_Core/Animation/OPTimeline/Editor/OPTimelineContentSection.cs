using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEditor.Experimental.GraphView;
using UnityEngine;
using UnityEngine.UIElements;

namespace OnePuz.OPTimeline.Editor
{
    /// <summary>
    /// the Timeline Content Section stores all the timeline Clips
    /// </summary>
    public class OPTimelineContentSection : VisualElement,IOPUpdatable,IACClickable
    {
        private readonly OPTimeline _timeline;
        private readonly List<OPTimelineClip> _timelineClips = new List<OPTimelineClip>();
        
        public VisualElement BackgroundContainer { get; }
        
        public OPTimelineContentSection(OPTimeline timeline)
        {
            _timeline = timeline;
            AddToClassList("content-section");

            BackgroundContainer = new VisualElement();
            BackgroundContainer.name = "background-container";
            BackgroundContainer.pickingMode = PickingMode.Ignore;
            Add(BackgroundContainer);
        }

        
        public void DestroyAndInstantiateClips(bool force = false)
        {
            if (!force && _timelineClips.Count == _timeline.Animator.GetClips().Count)
            {
                return;
            }
            foreach (var timelineClip in _timelineClips)
            {
                timelineClip.RemoveFromHierarchy();
            }
            _timelineClips.Clear();
            BackgroundContainer.Clear();

            for (var i = 0; i < _timeline.Animator.GetClips().Count; i++)
            {
                var newClip = _timeline.Animator.GetClips()[i];
                if (newClip == null) continue;
                AddTimelineClip(newClip);
            }
            Update();
        }

        public void AddTimelineClip(OPClip newClip)
        {
            var row = new VisualElement();
            row.pickingMode = PickingMode.Ignore;
            row.AddToClassList("timeline-row");
            BackgroundContainer.Add(row);
                
            var timelineClip = new OPTimelineClip(_timeline, newClip);
            timelineClip.Update();
            _timelineClips.Add(timelineClip);
            Add(timelineClip);
            timelineClip.style.opacity = 1;
        }
        
        public void Update()
        {
            foreach (var timelineClip in _timelineClips)
            {
                timelineClip.Update();
            }
        }

        public void Click(MouseButton mouseButton)
        {
            if (mouseButton == MouseButton.LeftMouse)
            {
                _timeline.AnimatorEditor.SetSelectedClip(null);
            }
            else if (mouseButton == MouseButton.RightMouse)
            {
                ShowContextMenu();
            }
        }
        
        private void ShowContextMenu()
        {
            var menu = new GenericMenu();
            var guiToScreenPoint = GUIUtility.GUIToScreenPoint(Event.current.mousePosition);
            menu.AddItem(new GUIContent("Add Clip"), false, () =>
            {
                SearchWindow.Open(new SearchWindowContext(guiToScreenPoint), _timeline.SearchWindow);
            });
            if (!string.IsNullOrEmpty(OPAnimatorEditor.CopiedClipJson))
            {
                menu.AddItem(new GUIContent("Paste Clip"), false, TryPasteCopiedClip);
            }
            else
            {
                menu.AddDisabledItem(new GUIContent("Paste Clip"),false);
            }
            
            menu.AddItem(new GUIContent("Trim Timeline"), false, () =>
            {
                var timelineLastClip = _timeline.Animator.GetTimelineLastClip();
                if (timelineLastClip == null) return;
                Undo.RecordObject(_timeline.Animator,"Trim Timeline");
                _timeline.Animator.FullDuration = timelineLastClip.GetEndTime();
            });
            menu.ShowAsContext();
        }

        public void DeleteClip(OPClip clip)
        {
            Undo.RecordObject(_timeline.Animator,"Delete Clip");
            if (_timeline.AnimatorEditor.IsPreviewInstance())
            {
                clip.OnPreviewModeChanged(false);
            }
            _timeline.Animator.RemoveClip(clip);
            var firstOrDefault = _timelineClips.FirstOrDefault(x=>x.Clip == clip);
            if (firstOrDefault != null)
            {
                _timelineClips.Remove(firstOrDefault);
                firstOrDefault.RemoveFromHierarchy();
            }
            BackgroundContainer.RemoveAt(BackgroundContainer.childCount-1);
            
            if(_timeline.AnimatorEditor.SelectedClip == clip) _timeline.AnimatorEditor.SetSelectedClip(null);
        }
        
        public OPTimelineClip GetTimelineClip(OPClip clip)
        {
            return _timelineClips.FirstOrDefault(x => x.Clip == clip);
        }

        public void DuplicateClip(OPTimelineClip timelineClip)
        {
            var clip = timelineClip.Clip;
            Undo.RecordObject(_timeline.Animator,"Duplicate Clip");
            var newClip = clip.Clone<OPClip>();
            newClip.Name = $"{newClip.Name} (Copy)";
            _timeline.Animator.GetClips().Insert(timelineClip.Index + 1,newClip);
            AddTimelineClip(newClip);
        }

        public void DuplicateClip(OPClip clip)
        {
            var timelineClip = GetTimelineClip(clip);
            if (timelineClip != null)
            {
                DuplicateClip(timelineClip);
            }
        }

        public void TryPasteCopiedClip()
        {
            if (_timeline.AnimatorEditor.TryGetClipFromCopy(out var result))
            {
                _timeline.AddClip(result);
            }
        }
    }
}