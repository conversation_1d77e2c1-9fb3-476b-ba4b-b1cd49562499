using UnityEditor;
using UnityEngine;

namespace OnePuz.OPTimeline.Editor
{
    [CustomPropertyDrawer(typeof(OPClipInitializer<>),true)]
    public class OPClipInitializerPropertyDrawer : PropertyDrawer
    {
        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            var useInitializer = property.FindPropertyRelative("enabled");
            var initialValue = property.FindPropertyRelative("value");
            
            position.width -= 150;
            position.x += 150;
            
            
            var wideMode = EditorGUIUtility.wideMode;
            EditorGUIUtility.wideMode = true;

            Rect labelRect = new Rect(position);
            labelRect.width = 60;
            labelRect.x -= 150;
            
            EditorGUI.BeginChangeCheck();
            var use = EditorGUI.ToggleLeft(labelRect, new GUIContent("Enable"), useInitializer.boolValue);

            if (EditorGUI.EndChangeCheck())
            {
                useInitializer.boolValue = use;
            }
            
            GUI.enabled = use;
            EditorGUI.PropertyField(position, initialValue,label);
            GUI.enabled = true;
            
            EditorGUIUtility.wideMode = wideMode;
        }

        public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
        {
            return EditorGUIUtility.singleLineHeight * 1;
        }
    }
}