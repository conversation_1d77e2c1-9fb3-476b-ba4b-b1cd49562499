using UnityEngine;
using UnityEngine.EventSystems;

namespace OnePuz.OPTimeline.Demos
{
    public class DemoButton : <PERSON>oB<PERSON><PERSON>our,IPointerEnterHandler,IPointerExitHandler
    {
        [SerializeField] private OPAnimatorPlayer animator;

        public void OnPointerEnter(PointerEventData eventData)
        {
            animator.Play();
            animator.Loop = true;
        }

        public void OnPointerExit(PointerEventData eventData)
        {
            animator.Loop = false;
        }
    }
}