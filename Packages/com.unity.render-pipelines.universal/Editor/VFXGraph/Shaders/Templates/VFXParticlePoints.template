{
	SubShader
	{	
		${VFXInclude("Shaders/VFXParticleHeader.template")}
		${VFXInclude("Shaders/ParticlePoints/PassSelection.template")}
		${VFXInclude("Shaders/ParticlePoints/PassDepth.template"),IS_OPAQUE_PARTICLE}
		${VFXInclude("Shaders/ParticlePoints/PassDepthNormal.template"),IS_OPAQUE_PARTICLE}
		${VFXInclude("Shaders/ParticlePoints/PassVelocity.template"),USE_MOTION_VECTORS_PASS}
		${VFXInclude("Shaders/ParticlePoints/PassForward.template")}
		${VFXInclude("Shaders/ParticlePoints/PassShadowCaster.template"),USE_CAST_SHADOWS_PASS}
		${VFXIncludeRP("Templates/ParticlePoints/PassForward2D.template")}
	}
}
