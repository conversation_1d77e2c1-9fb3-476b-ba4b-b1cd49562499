#include "Packages/com.unity.render-pipelines.universal/Runtime/VFXGraph/Shaders/VFXLit.hlsl"

#ifndef VFX_SHADERGRAPH

#if VFX_MATERIAL_TYPE_SIX_WAY_SMOKE
#define SurfaceData SixWaySurfaceData
#endif

void VFXGetURPLitData(out SurfaceData surfaceData, out InputData inputData, VFX_VARYING_PS_INPUTS i, float3 normalWS, const VFXUVData uvData, bool frontFace, uint2 tileIndex)
{
    float3 posRWS = VFXGetPositionRWS(i);
    float4 posSS = i.VFX_VARYING_POSCS;
    PositionInputs posInput = GetPositionInput(posSS.xy, _ScreenSize.zw, posSS.z, posSS.w, posRWS, tileIndex);

    surfaceData = VFXGetSurfaceData(i, normalWS, uvData);
    inputData = VFXGetInputData(i, posInput, normalWS, frontFace);
}
#endif


#include "Packages/com.unity.render-pipelines.universal/Runtime/VFXGraph/Shaders/VFXLitPixelOutput.hlsl"

